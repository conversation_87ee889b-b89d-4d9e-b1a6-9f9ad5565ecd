import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  StarIcon,
  ChatBubbleLeftRightIcon,
  FunnelIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const FeedbackManagement = () => {
  const [feedbacks, setFeedbacks] = useState([]);
  const [filteredFeedbacks, setFilteredFeedbacks] = useState([]);
  const [ratingFilter, setRatingFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFeedback, setSelectedFeedback] = useState(null);

  // Mock feedback data
  useEffect(() => {
    const mockFeedbacks = [
      {
        id: '1',
        orderNumber: 'ZCH1640995200000',
        customerName: '<PERSON>',
        customerEmail: '<EMAIL>',
        overallRating: 5,
        deliveryRating: 5,
        foodQuality: 'excellent',
        comment: 'Amazing food and fast delivery! The fried rice was perfectly seasoned and the chicken was tender. Will definitely order again!',
        wouldRecommend: true,
        status: 'new',
        submittedAt: '2024-01-20T14:30:00Z',
        orderItems: ['Fried Rice with Chicken', 'Fried Plantains']
      },
      {
        id: '2',
        orderNumber: 'ZCH1640995300000',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        overallRating: 4,
        deliveryRating: 4,
        foodQuality: 'good',
        comment: 'Good food overall. The fufu was authentic and delicious. Delivery was on time.',
        wouldRecommend: true,
        status: 'responded',
        submittedAt: '2024-01-19T16:45:00Z',
        response: 'Thank you for your feedback! We\'re glad you enjoyed the fufu.',
        respondedAt: '2024-01-19T18:00:00Z',
        orderItems: ['Water Fufu and Eru']
      },
      {
        id: '3',
        orderNumber: 'ZCH1640995400000',
        customerName: 'Mike Johnson',
        customerEmail: '<EMAIL>',
        overallRating: 3,
        deliveryRating: 2,
        foodQuality: 'average',
        comment: 'Food was okay but delivery took longer than expected. The fish was a bit cold when it arrived.',
        wouldRecommend: false,
        status: 'flagged',
        submittedAt: '2024-01-18T12:20:00Z',
        orderItems: ['Grilled Fish', 'Pepper Soup']
      },
      {
        id: '4',
        orderNumber: 'ZCH1640995500000',
        customerName: 'Mary Kone',
        customerEmail: '<EMAIL>',
        overallRating: 5,
        deliveryRating: 5,
        foodQuality: 'excellent',
        comment: 'Best restaurant in Douala! Everything was perfect. Thank you!',
        wouldRecommend: true,
        status: 'responded',
        submittedAt: '2024-01-17T19:15:00Z',
        response: 'Thank you so much for your kind words! We appreciate your support.',
        respondedAt: '2024-01-17T20:30:00Z',
        orderItems: ['Fried Rice with Chicken', 'Grilled Fish']
      }
    ];
    setFeedbacks(mockFeedbacks);
    setFilteredFeedbacks(mockFeedbacks);
  }, []);

  // Filter feedbacks
  useEffect(() => {
    let filtered = feedbacks;

    if (ratingFilter !== 'all') {
      const rating = parseInt(ratingFilter);
      filtered = filtered.filter(feedback => feedback.overallRating === rating);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(feedback => feedback.status === statusFilter);
    }

    if (searchTerm) {
      filtered = filtered.filter(feedback =>
        feedback.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feedback.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feedback.comment.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredFeedbacks(filtered);
  }, [feedbacks, ratingFilter, statusFilter, searchTerm]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'new':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'responded':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'flagged':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'new':
        return 'bg-yellow-100 text-yellow-800';
      case 'responded':
        return 'bg-green-100 text-green-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleStatusUpdate = (feedbackId, newStatus) => {
    setFeedbacks(feedbacks.map(feedback =>
      feedback.id === feedbackId
        ? { ...feedback, status: newStatus }
        : feedback
    ));
  };

  const averageRating = feedbacks.length > 0
    ? (feedbacks.reduce((sum, feedback) => sum + feedback.overallRating, 0) / feedbacks.length).toFixed(1)
    : 0;

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: feedbacks.filter(f => f.overallRating === rating).length,
    percentage: feedbacks.length > 0
      ? (feedbacks.filter(f => f.overallRating === rating).length / feedbacks.length * 100).toFixed(1)
      : 0
  }));

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Feedback Management</h1>
            <p className="text-gray-600 mt-2">Monitor and respond to customer feedback</p>
          </div>
          <div className="text-sm text-gray-500">
            Total Feedback: {filteredFeedbacks.length}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <StarIconSolid className="h-8 w-8 text-yellow-400" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Rating</p>
                <p className="text-2xl font-bold text-gray-900">{averageRating}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <ChatBubbleLeftRightIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{feedbacks.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Response</p>
                <p className="text-2xl font-bold text-gray-900">
                  {feedbacks.filter(f => f.status === 'new').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Responded</p>
                <p className="text-2xl font-bold text-gray-900">
                  {feedbacks.filter(f => f.status === 'responded').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Rating Distribution */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Rating Distribution</h2>
            <div className="space-y-3">
              {ratingDistribution.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center">
                  <div className="flex items-center w-16">
                    <span className="text-sm font-medium">{rating}</span>
                    <StarIconSolid className="h-4 w-4 text-yellow-400 ml-1" />
                  </div>
                  <div className="flex-1 mx-3">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-sm text-gray-600 w-12">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Feedback List */}
          <div className="lg:col-span-3">
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search feedback..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
                <select
                  value={ratingFilter}
                  onChange={(e) => setRatingFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="all">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="new">New</option>
                  <option value="responded">Responded</option>
                  <option value="flagged">Flagged</option>
                </select>
              </div>
            </div>

            {/* Feedback Items */}
            <div className="space-y-4">
              {filteredFeedbacks.map((feedback, index) => (
                <motion.div
                  key={feedback.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(feedback.status)}
                      <div>
                        <h3 className="font-semibold text-gray-900">{feedback.customerName}</h3>
                        <p className="text-sm text-gray-500">Order #{feedback.orderNumber}</p>
                        <p className="text-xs text-gray-500">{formatDate(feedback.submittedAt)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(feedback.status)}`}>
                        {feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1)}
                      </span>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <StarIconSolid
                            key={i}
                            className={`h-4 w-4 ${
                              i < feedback.overallRating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-gray-700">{feedback.comment}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                    <div>
                      <span className="text-gray-500">Food Quality:</span>
                      <span className="ml-2 font-medium capitalize">{feedback.foodQuality}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Delivery Rating:</span>
                      <span className="ml-2 font-medium">{feedback.deliveryRating}/5</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Would Recommend:</span>
                      <span className={`ml-2 font-medium ${feedback.wouldRecommend ? 'text-green-600' : 'text-red-600'}`}>
                        {feedback.wouldRecommend ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <span className="text-sm text-gray-500">Order Items:</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {feedback.orderItems.map((item, itemIndex) => (
                        <span key={itemIndex} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>

                  {feedback.response && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                      <p className="text-sm text-green-800">
                        <strong>Your Response:</strong> {feedback.response}
                      </p>
                      <p className="text-xs text-green-600 mt-1">
                        Responded on {formatDate(feedback.respondedAt)}
                      </p>
                    </div>
                  )}

                  <div className="flex space-x-2">
                    {feedback.status === 'new' && (
                      <>
                        <button
                          onClick={() => handleStatusUpdate(feedback.id, 'responded')}
                          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                        >
                          Mark as Responded
                        </button>
                        <button
                          onClick={() => handleStatusUpdate(feedback.id, 'flagged')}
                          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                        >
                          Flag for Review
                        </button>
                      </>
                    )}

                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                      <EyeIcon className="h-4 w-4 inline mr-1" />
                      View Order
                    </button>

                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                      Contact Customer
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredFeedbacks.length === 0 && (
              <div className="text-center py-12">
                <ChatBubbleLeftRightIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No feedback found
                </h3>
                <p className="text-gray-600">
                  No feedback matches your current filters.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackManagement;
