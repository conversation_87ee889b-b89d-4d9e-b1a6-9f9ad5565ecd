import express from 'express';
import axios from 'axios';
import crypto from 'crypto';
import Order from '../models/Order.js';
import Payment from '../models/Payment.js';
import auth from '../middleware/auth.js';

const router = express.Router();

// MTN Mobile Money Configuration
const MTN_CONFIG = {
  baseURL: process.env.MTN_API_BASE_URL || 'https://sandbox.momodeveloper.mtn.com',
  subscriptionKey: process.env.MTN_SUBSCRIPTION_KEY,
  apiUser: process.env.MTN_API_USER,
  apiKey: process.env.MTN_API_KEY,
  environment: process.env.MTN_ENVIRONMENT || 'sandbox'
};

// Orange Money Configuration
const ORANGE_CONFIG = {
  baseURL: process.env.ORANGE_API_BASE_URL || 'https://api.orange.com',
  clientId: process.env.ORANGE_CLIENT_ID,
  clientSecret: process.env.ORANGE_CLIENT_SECRET,
  merchantKey: process.env.ORANGE_MERCHANT_KEY,
  environment: process.env.ORANGE_ENVIRONMENT || 'sandbox'
};

// Generate transaction reference
const generateTransactionRef = () => {
  return `ZCH_${Date.now()}_${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
};

// @desc    Get payment methods
// @route   GET /api/payments/methods
// @access  Public
router.get('/methods', (req, res) => {
  res.json({
    success: true,
    data: {
      methods: [
        {
          id: 'mtn-mobile-money',
          name: 'MTN Mobile Money',
          type: 'mobile_money',
          provider: 'MTN',
          logo: '/images/mtn-logo.png',
          available: true,
          description: 'Pay with your MTN Mobile Money account'
        },
        {
          id: 'orange-money',
          name: 'Orange Money',
          type: 'mobile_money',
          provider: 'Orange',
          logo: '/images/orange-logo.png',
          available: true,
          description: 'Pay with your Orange Money account'
        },
        {
          id: 'cash-on-delivery',
          name: 'Cash on Delivery',
          type: 'cash',
          provider: 'COD',
          logo: '/images/cash-logo.png',
          available: true,
          description: 'Pay with cash when your order arrives'
        }
      ]
    }
  });
});

// @desc    Initiate MTN Mobile Money payment
// @route   POST /api/payments/mtn/initiate
// @access  Private
router.post('/mtn/initiate', auth, async (req, res) => {
  try {
    const { orderId, phoneNumber, amount } = req.body;

    // Validate input
    if (!orderId || !phoneNumber || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Order ID, phone number, and amount are required'
      });
    }

    // Validate phone number format (Cameroon MTN format)
    const phoneRegex = /^237(67|68|69|65|66)\d{7}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid MTN phone number format. Use format: 237XXXXXXXX'
      });
    }

    // Find the order
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Generate transaction reference
    const transactionRef = generateTransactionRef();

    // Create payment record
    const payment = new Payment({
      orderId,
      userId: req.user.id,
      amount,
      currency: 'XAF',
      method: 'mtn-mobile-money',
      provider: 'MTN',
      transactionRef,
      phoneNumber,
      status: 'pending',
      metadata: {
        orderNumber: order.orderNumber,
        customerName: req.user.name
      }
    });

    await payment.save();

    // In production, integrate with actual MTN MoMo API
    if (MTN_CONFIG.environment === 'production') {
      try {
        // Get MTN API token
        const tokenResponse = await axios.post(
          `${MTN_CONFIG.baseURL}/collection/token/`,
          {},
          {
            headers: {
              'Authorization': `Basic ${Buffer.from(`${MTN_CONFIG.apiUser}:${MTN_CONFIG.apiKey}`).toString('base64')}`,
              'Ocp-Apim-Subscription-Key': MTN_CONFIG.subscriptionKey,
              'X-Target-Environment': MTN_CONFIG.environment
            }
          }
        );

        const accessToken = tokenResponse.data.access_token;

        // Initiate payment request
        const paymentResponse = await axios.post(
          `${MTN_CONFIG.baseURL}/collection/v1_0/requesttopay`,
          {
            amount: amount.toString(),
            currency: 'EUR', // MTN API uses EUR for XAF
            externalId: transactionRef,
            payer: {
              partyIdType: 'MSISDN',
              partyId: phoneNumber
            },
            payerMessage: `Payment for order ${order.orderNumber}`,
            payeeNote: 'Zina Chop House Order Payment'
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'X-Reference-Id': transactionRef,
              'X-Target-Environment': MTN_CONFIG.environment,
              'Ocp-Apim-Subscription-Key': MTN_CONFIG.subscriptionKey,
              'Content-Type': 'application/json'
            }
          }
        );

        // Update payment with MTN response
        payment.providerTransactionId = transactionRef;
        payment.providerResponse = paymentResponse.data;
        await payment.save();

      } catch (mtnError) {
        console.error('MTN API Error:', mtnError.response?.data || mtnError.message);
        payment.status = 'failed';
        payment.errorMessage = mtnError.response?.data?.message || 'MTN API error';
        await payment.save();

        return res.status(500).json({
          success: false,
          message: 'Payment initiation failed',
          error: mtnError.response?.data?.message || 'MTN API error'
        });
      }
    } else {
      // Sandbox/Demo mode - simulate successful initiation
      payment.providerTransactionId = transactionRef;
      payment.status = 'processing';
      await payment.save();

      // Simulate async payment processing (in real app, this would be a webhook)
      setTimeout(async () => {
        try {
          const updatedPayment = await Payment.findById(payment._id);
          if (updatedPayment && updatedPayment.status === 'processing') {
            updatedPayment.status = 'completed';
            updatedPayment.completedAt = new Date();
            await updatedPayment.save();

            // Update order status
            const updatedOrder = await Order.findById(orderId);
            if (updatedOrder) {
              updatedOrder.paymentStatus = 'paid';
              updatedOrder.status = 'confirmed';
              await updatedOrder.save();
            }
          }
        } catch (error) {
          console.error('Demo payment completion error:', error);
        }
      }, 5000); // Simulate 5 second processing time
    }

    res.json({
      success: true,
      message: 'Payment initiated successfully',
      data: {
        transactionRef,
        paymentId: payment._id,
        status: payment.status,
        amount,
        currency: 'XAF',
        method: 'MTN Mobile Money'
      }
    });

  } catch (error) {
    console.error('MTN payment initiation error:', error);
    res.status(500).json({
      success: false,
      message: 'Payment initiation failed',
      error: error.message
    });
  }
});

// @desc    Initiate Orange Money payment
// @route   POST /api/payments/orange/initiate
// @access  Private
router.post('/orange/initiate', auth, async (req, res) => {
  try {
    const { orderId, phoneNumber, amount } = req.body;

    // Validate input
    if (!orderId || !phoneNumber || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Order ID, phone number, and amount are required'
      });
    }

    // Validate phone number format (Cameroon Orange format)
    const phoneRegex = /^237(69|65|66|67|68)\d{7}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid Orange phone number format. Use format: 237XXXXXXXX'
      });
    }

    // Find the order
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Generate transaction reference
    const transactionRef = generateTransactionRef();

    // Create payment record
    const payment = new Payment({
      orderId,
      userId: req.user.id,
      amount,
      currency: 'XAF',
      method: 'orange-money',
      provider: 'Orange',
      transactionRef,
      phoneNumber,
      status: 'pending',
      metadata: {
        orderNumber: order.orderNumber,
        customerName: req.user.name
      }
    });

    await payment.save();

    // In production, integrate with actual Orange Money API
    if (ORANGE_CONFIG.environment === 'production') {
      try {
        // Get Orange API token
        const tokenResponse = await axios.post(
          `${ORANGE_CONFIG.baseURL}/oauth/v2/token`,
          {
            grant_type: 'client_credentials'
          },
          {
            headers: {
              'Authorization': `Basic ${Buffer.from(`${ORANGE_CONFIG.clientId}:${ORANGE_CONFIG.clientSecret}`).toString('base64')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        );

        const accessToken = tokenResponse.data.access_token;

        // Initiate payment request
        const paymentResponse = await axios.post(
          `${ORANGE_CONFIG.baseURL}/orange-money-webpay/cm/v1/webpayment`,
          {
            merchant_key: ORANGE_CONFIG.merchantKey,
            currency: 'XAF',
            order_id: transactionRef,
            amount: amount,
            return_url: `${process.env.FRONTEND_URL}/payment/success`,
            cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`,
            notif_url: `${process.env.API_URL}/api/payments/orange/webhook`,
            lang: 'fr',
            reference: `Order ${order.orderNumber}`
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        // Update payment with Orange response
        payment.providerTransactionId = paymentResponse.data.pay_token;
        payment.providerResponse = paymentResponse.data;
        await payment.save();

      } catch (orangeError) {
        console.error('Orange API Error:', orangeError.response?.data || orangeError.message);
        payment.status = 'failed';
        payment.errorMessage = orangeError.response?.data?.message || 'Orange API error';
        await payment.save();

        return res.status(500).json({
          success: false,
          message: 'Payment initiation failed',
          error: orangeError.response?.data?.message || 'Orange API error'
        });
      }
    } else {
      // Sandbox/Demo mode - simulate successful initiation
      payment.providerTransactionId = transactionRef;
      payment.status = 'processing';
      await payment.save();

      // Simulate async payment processing
      setTimeout(async () => {
        try {
          const updatedPayment = await Payment.findById(payment._id);
          if (updatedPayment && updatedPayment.status === 'processing') {
            updatedPayment.status = 'completed';
            updatedPayment.completedAt = new Date();
            await updatedPayment.save();

            // Update order status
            const updatedOrder = await Order.findById(orderId);
            if (updatedOrder) {
              updatedOrder.paymentStatus = 'paid';
              updatedOrder.status = 'confirmed';
              await updatedOrder.save();
            }
          }
        } catch (error) {
          console.error('Demo payment completion error:', error);
        }
      }, 5000);
    }

    res.json({
      success: true,
      message: 'Payment initiated successfully',
      data: {
        transactionRef,
        paymentId: payment._id,
        status: payment.status,
        amount,
        currency: 'XAF',
        method: 'Orange Money'
      }
    });

  } catch (error) {
    console.error('Orange payment initiation error:', error);
    res.status(500).json({
      success: false,
      message: 'Payment initiation failed',
      error: error.message
    });
  }
});

export default router;
