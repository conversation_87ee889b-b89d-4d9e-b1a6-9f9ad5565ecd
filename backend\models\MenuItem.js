import mongoose from 'mongoose';

const menuItemSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Menu item name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'main-course',
      'side-dish',
      'special',
      'beverage',
      'dessert'
    ]
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    publicId: String, // Cloudinary public ID for deletion
    alt: String
  }],
  ingredients: [String],
  allergens: [String],
  nutritionalInfo: {
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    fiber: Number
  },
  availability: {
    isAvailable: {
      type: Boolean,
      default: true
    },
    daysOfWeek: [{
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    }],
    specialDay: {
      type: String,
      enum: ['njama-njama-friday', 'regular-friday']
    }
  },
  preparationTime: {
    type: Number, // in minutes
    default: 30
  },
  isSpecial: {
    type: Boolean,
    default: false
  },
  specialType: {
    type: String,
    enum: ['weekly-special', 'seasonal', 'limited-time'],
    required: function() {
      return this.isSpecial;
    }
  },
  tags: [String],
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  orderCount: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes for better query performance
menuItemSchema.index({ category: 1 });
menuItemSchema.index({ 'availability.isAvailable': 1 });
menuItemSchema.index({ 'availability.daysOfWeek': 1 });
menuItemSchema.index({ isSpecial: 1 });
menuItemSchema.index({ isActive: 1 });
menuItemSchema.index({ name: 'text', description: 'text' }); // Text search

// Virtual for full image URLs
menuItemSchema.virtual('imageUrls').get(function() {
  return this.images.map(img => img.url);
});

// Method to check if item is available on a specific day
menuItemSchema.methods.isAvailableOnDay = function(dayOfWeek) {
  if (!this.availability.isAvailable || !this.isActive) {
    return false;
  }
  
  if (this.availability.daysOfWeek.length === 0) {
    return true; // Available all days if no specific days set
  }
  
  return this.availability.daysOfWeek.includes(dayOfWeek.toLowerCase());
};

// Method to update rating
menuItemSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.rating.average * this.rating.count) + newRating;
  this.rating.count += 1;
  this.rating.average = totalRating / this.rating.count;
  return this.save();
};

export default mongoose.model('MenuItem', menuItemSchema);
