import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import {
  ClockIcon,
  CheckCircleIcon,
  TruckIcon,
  PhoneIcon,
  MapPinIcon,
  ArrowTopRightOnSquareIcon,
  CameraIcon
} from '@heroicons/react/24/outline';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock delivery orders data
  useEffect(() => {
    const mockOrders = [
      {
        id: '1',
        orderNumber: 'ZCH1640995200000',
        customerInfo: {
          name: '<PERSON>',
          phone: '+237123456789',
          address: {
            street: '123 Main St',
            city: 'Douala',
            landmark: 'Near Total Station'
          },
          instructions: 'Call when you arrive at the gate'
        },
        items: [
          { name: 'Fried Rice with Chicken', quantity: 2, price: 2500 },
          { name: 'Fried Plantains', quantity: 1, price: 1500 }
        ],
        totalAmount: 6500,
        status: 'ready-for-pickup',
        paymentMethod: 'cash-on-delivery',
        estimatedTime: '15 mins',
        distance: '2.5 km',
        createdAt: '2024-01-20T10:30:00Z'
      },
      {
        id: '2',
        orderNumber: 'ZCH1640995300000',
        customerInfo: {
          name: 'Jane Smith',
          phone: '+237987654321',
          address: {
            street: '456 Oak Ave',
            city: 'Douala',
            landmark: 'Blue building opposite pharmacy'
          },
          instructions: 'Ring the doorbell twice'
        },
        items: [
          { name: 'Water Fufu and Eru', quantity: 2, price: 3000 }
        ],
        totalAmount: 6000,
        status: 'out-for-delivery',
        paymentMethod: 'mtn-mobile-money',
        estimatedTime: '25 mins',
        distance: '4.1 km',
        createdAt: '2024-01-20T11:15:00Z'
      },
      {
        id: '3',
        orderNumber: 'ZCH1640995400000',
        customerInfo: {
          name: 'Mike Johnson',
          phone: '+237555123456',
          address: {
            street: '789 Pine St',
            city: 'Douala',
            landmark: 'Green house with red gate'
          }
        },
        items: [
          { name: 'Grilled Fish', quantity: 1, price: 4000 },
          { name: 'Pepper Soup', quantity: 1, price: 2000 }
        ],
        totalAmount: 6500,
        status: 'delivered',
        paymentMethod: 'orange-money',
        estimatedTime: 'Completed',
        distance: '3.2 km',
        createdAt: '2024-01-20T09:00:00Z',
        deliveredAt: '2024-01-20T09:25:00Z'
      }
    ];
    setOrders(mockOrders);
    setFilteredOrders(mockOrders);
  }, []);

  // Filter orders based on status
  useEffect(() => {
    if (statusFilter === 'all') {
      setFilteredOrders(orders);
    } else {
      setFilteredOrders(orders.filter(order => order.status === statusFilter));
    }
  }, [orders, statusFilter]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ready-for-pickup':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'out-for-delivery':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready-for-pickup':
        return 'bg-yellow-100 text-yellow-800';
      case 'out-for-delivery':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    setOrders(orders.map(order =>
      order.id === orderId
        ? {
            ...order,
            status: newStatus,
            ...(newStatus === 'delivered' && { deliveredAt: new Date().toISOString() })
          }
        : order
    ));
    toast.success(`Order status updated to ${newStatus.replace('-', ' ')}`);
  };

  const handleCallCustomer = (phone) => {
    window.open(`tel:${phone}`);
  };

  const handleOpenMaps = (address) => {
    const query = encodeURIComponent(`${address.street}, ${address.city}, Cameroon`);
    window.open(`https://www.google.com/maps/search/?api=1&query=${query}`, '_blank');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US').format(amount);
  };

  const statusOptions = [
    { value: 'all', label: 'All Orders' },
    { value: 'ready-for-pickup', label: 'Ready for Pickup' },
    { value: 'out-for-delivery', label: 'Out for Delivery' },
    { value: 'delivered', label: 'Delivered' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Delivery Orders</h1>
            <p className="text-gray-600 mt-2">Manage your assigned delivery orders</p>
          </div>
          <div className="mt-4 sm:mt-0 text-sm text-gray-500">
            Active Orders: {filteredOrders.filter(o => o.status !== 'delivered').length}
          </div>
        </div>

        {/* Status Filter */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Status
          </label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
            >
              <div className="p-6">
                {/* Order Header */}
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                  <div className="flex items-center space-x-3 mb-2 lg:mb-0">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Order #{order.orderNumber}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatDate(order.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">ETA</p>
                      <p className="font-medium">{order.estimatedTime}</p>
                    </div>
                  </div>
                </div>

                {/* Customer & Delivery Info */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h4>
                    <div className="space-y-2">
                      <p className="font-medium">{order.customerInfo.name}</p>
                      <div className="flex items-center">
                        <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600">{order.customerInfo.phone}</span>
                      </div>
                      <div className="flex items-start">
                        <MapPinIcon className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                        <div className="text-sm text-gray-600">
                          <p>{order.customerInfo.address.street}</p>
                          <p>{order.customerInfo.address.city}</p>
                          {order.customerInfo.address.landmark && (
                            <p className="text-gray-500 italic">Landmark: {order.customerInfo.address.landmark}</p>
                          )}
                        </div>
                      </div>
                      {order.customerInfo.instructions && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <p className="text-sm text-yellow-800">
                            <strong>Instructions:</strong> {order.customerInfo.instructions}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Order Details</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Distance</span>
                        <span className="font-medium">{order.distance}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Payment</span>
                        <span className="font-medium">
                          {order.paymentMethod.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Total Amount</span>
                        <span className="font-bold text-orange-600">{formatCurrency(order.totalAmount)} XAF</span>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Items ({order.items.length})</h5>
                      <div className="space-y-1">
                        {order.items.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex justify-between text-sm">
                            <span className="text-gray-600">{item.name} x{item.quantity}</span>
                            <span className="font-medium">{formatCurrency(item.price * item.quantity)} XAF</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                  {order.status === 'ready-for-pickup' && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'out-for-delivery')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                    >
                      Start Delivery
                    </button>
                  )}

                  {order.status === 'out-for-delivery' && (
                    <>
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'delivered')}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                      >
                        Mark Delivered
                      </button>
                      <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium">
                        <CameraIcon className="h-4 w-4 inline mr-1" />
                        Take Photo
                      </button>
                    </>
                  )}

                  <button
                    onClick={() => handleCallCustomer(order.customerInfo.phone)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                  >
                    <PhoneIcon className="h-4 w-4 inline mr-1" />
                    Call Customer
                  </button>

                  <button
                    onClick={() => handleOpenMaps(order.customerInfo.address)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                  >
                    <ArrowTopRightOnSquareIcon className="h-4 w-4 inline mr-1" />
                    Open Maps
                  </button>

                  {order.status === 'delivered' && order.deliveredAt && (
                    <div className="ml-auto text-sm text-green-600">
                      Delivered at {formatDate(order.deliveredAt)}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <TruckIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No orders found
            </h3>
            <p className="text-gray-600">
              {statusFilter === 'all'
                ? "No delivery orders assigned to you yet."
                : `No orders with status "${statusFilter.replace('-', ' ')}" found.`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;
