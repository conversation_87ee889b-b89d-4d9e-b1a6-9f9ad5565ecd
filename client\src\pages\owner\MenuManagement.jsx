import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PhotoIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import {
  friedRice,
  waterFufu,
  grilledFish,
  friedPlantains,
  pepperSoup,
  jollofRice,
  ndole,
  poundedYam,
  suya,
  akara,
  moimoi,
  chickenStew,
  beefStew
} from '../../assets';

const MenuManagement = () => {
  const [menuItems, setMenuItems] = useState([]);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm();

  const categories = [
    'all',
    'rice-dishes',
    'traditional',
    'grilled',
    'soups',
    'sides',
    'beverages',
    'desserts'
  ];

  // Mock menu items with your uploaded images
  useEffect(() => {
    setMenuItems([
      {
        _id: '1',
        name: 'Fried Rice with Chicken',
        description: 'Delicious fried rice with tender chicken pieces, vegetables, and aromatic spices',
        price: 2500,
        category: 'rice-dishes',
        images: [{ url: friedRice }],
        isAvailable: true,
        preparationTime: 15,
        ingredients: ['Rice', 'Chicken', 'Vegetables', 'Spices'],
        nutritionalInfo: { calories: 450, protein: 25, carbs: 55, fat: 12 }
      },
      {
        _id: '2',
        name: 'Water Fufu and Eru',
        description: 'Traditional Cameroon dish with water fufu and eru vegetables',
        price: 3000,
        category: 'traditional',
        images: [{ url: waterFufu }],
        isAvailable: true,
        preparationTime: 25,
        ingredients: ['Cassava', 'Eru leaves', 'Palm oil', 'Meat'],
        nutritionalInfo: { calories: 380, protein: 20, carbs: 45, fat: 15 }
      },
      {
        _id: '3',
        name: 'Grilled Fish',
        description: 'Fresh fish grilled to perfection with local spices',
        price: 4000,
        category: 'grilled',
        images: [{ url: grilledFish }],
        isAvailable: true,
        preparationTime: 20,
        ingredients: ['Fresh Fish', 'Spices', 'Lemon', 'Herbs'],
        nutritionalInfo: { calories: 320, protein: 35, carbs: 5, fat: 18 }
      },
      {
        _id: '4',
        name: 'Fried Plantains',
        description: 'Sweet fried plantains, a perfect side dish',
        price: 1500,
        category: 'sides',
        images: [{ url: friedPlantains }],
        isAvailable: true,
        preparationTime: 10,
        ingredients: ['Plantains', 'Oil', 'Salt'],
        nutritionalInfo: { calories: 180, protein: 2, carbs: 35, fat: 8 }
      },
      {
        _id: '5',
        name: 'Pepper Soup',
        description: 'Spicy traditional pepper soup with meat',
        price: 2000,
        category: 'soups',
        images: [{ url: pepperSoup }],
        isAvailable: false,
        preparationTime: 30,
        ingredients: ['Meat', 'Pepper', 'Spices', 'Vegetables'],
        nutritionalInfo: { calories: 250, protein: 22, carbs: 8, fat: 15 }
      },
      {
        _id: '6',
        name: 'Jollof Rice',
        description: 'Flavorful West African jollof rice with spices and vegetables',
        price: 2800,
        category: 'rice-dishes',
        images: [{ url: jollofRice }],
        isAvailable: true,
        preparationTime: 20,
        ingredients: ['Rice', 'Tomatoes', 'Onions', 'Spices'],
        nutritionalInfo: { calories: 420, protein: 8, carbs: 65, fat: 14 }
      },
      {
        _id: '7',
        name: 'Ndole',
        description: 'Traditional Cameroonian ndole with groundnuts and meat',
        price: 3500,
        category: 'traditional',
        images: [{ url: ndole }],
        isAvailable: true,
        preparationTime: 35,
        ingredients: ['Ndole leaves', 'Groundnuts', 'Meat', 'Fish'],
        nutritionalInfo: { calories: 480, protein: 28, carbs: 25, fat: 32 }
      },
      {
        _id: '8',
        name: 'Pounded Yam',
        description: 'Smooth pounded yam served with your choice of soup',
        price: 2200,
        category: 'traditional',
        images: [{ url: poundedYam }],
        isAvailable: true,
        preparationTime: 25,
        ingredients: ['Yam', 'Water'],
        nutritionalInfo: { calories: 320, protein: 4, carbs: 78, fat: 1 }
      },
      {
        _id: '9',
        name: 'Suya',
        description: 'Spicy grilled meat skewers with traditional spices',
        price: 1800,
        category: 'grilled',
        images: [{ url: suya }],
        isAvailable: true,
        preparationTime: 15,
        ingredients: ['Beef', 'Suya spice', 'Onions'],
        nutritionalInfo: { calories: 280, protein: 25, carbs: 8, fat: 18 }
      },
      {
        _id: '10',
        name: 'Akara',
        description: 'Deep-fried bean cakes, crispy outside and soft inside',
        price: 1200,
        category: 'sides',
        images: [{ url: akara }],
        isAvailable: true,
        preparationTime: 12,
        ingredients: ['Black-eyed peas', 'Onions', 'Pepper', 'Oil'],
        nutritionalInfo: { calories: 220, protein: 12, carbs: 18, fat: 12 }
      }
    ]);
  }, []);

  const filteredItems = selectedCategory === 'all'
    ? menuItems
    : menuItems.filter(item => item.category === selectedCategory);

  const onSubmit = async (data) => {
    try {
      if (editingItem) {
        // Update existing item
        setMenuItems(menuItems.map(item =>
          item._id === editingItem._id
            ? { ...item, ...data, price: parseFloat(data.price) }
            : item
        ));
        toast.success('Menu item updated successfully!');
        setEditingItem(null);
      } else {
        // Add new item
        const newItem = {
          _id: Date.now().toString(),
          ...data,
          price: parseFloat(data.price),
          images: [{ url: '/src/assets/images/placeholder-food.jpg' }],
          isAvailable: true
        };
        setMenuItems([...menuItems, newItem]);
        toast.success('Menu item added successfully!');
        setIsAddingItem(false);
      }
      reset();
    } catch (error) {
      toast.error('Failed to save menu item');
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setIsAddingItem(true);
    setValue('name', item.name);
    setValue('description', item.description);
    setValue('price', item.price);
    setValue('category', item.category);
    setValue('preparationTime', item.preparationTime);
  };

  const handleDelete = async (itemId) => {
    if (window.confirm('Are you sure you want to delete this menu item?')) {
      setMenuItems(menuItems.filter(item => item._id !== itemId));
      toast.success('Menu item deleted successfully!');
    }
  };

  const toggleAvailability = (itemId) => {
    setMenuItems(menuItems.map(item =>
      item._id === itemId
        ? { ...item, isAvailable: !item.isAvailable }
        : item
    ));
    toast.success('Item availability updated!');
  };

  const handleCancel = () => {
    setIsAddingItem(false);
    setEditingItem(null);
    reset();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Menu Management</h1>
            <p className="text-gray-600 mt-2">Manage your restaurant's menu items</p>
          </div>
          <button
            onClick={() => setIsAddingItem(true)}
            className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Menu Item
          </button>
        </div>

        {/* Category Filter */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-orange-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
              </button>
            ))}
          </div>
        </div>

        {/* Add/Edit Form */}
        {isAddingItem && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-6 mb-6"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {editingItem ? 'Edit Menu Item' : 'Add New Menu Item'}
            </h2>
            <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Item Name *
                </label>
                <input
                  {...register('name', { required: 'Item name is required' })}
                  type="text"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter item name"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price (XAF) *
                </label>
                <input
                  {...register('price', {
                    required: 'Price is required',
                    min: { value: 0, message: 'Price must be positive' }
                  })}
                  type="number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter price"
                />
                {errors.price && (
                  <p className="text-red-500 text-sm mt-1">{errors.price.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  {...register('category', { required: 'Category is required' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="">Select category</option>
                  {categories.slice(1).map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preparation Time (minutes)
                </label>
                <input
                  {...register('preparationTime')}
                  type="number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter preparation time"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  {...register('description', { required: 'Description is required' })}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter item description"
                />
                {errors.description && (
                  <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
                )}
              </div>

              <div className="md:col-span-2 flex space-x-4">
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                >
                  {editingItem ? 'Update Item' : 'Add Item'}
                </motion.button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </form>
          </motion.div>
        )}

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm overflow-hidden"
            >
              {/* Item Image */}
              <div className="relative h-48">
                <img
                  src={item.images[0]?.url || '/src/assets/images/placeholder-food.jpg'}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 right-2">
                  <button
                    onClick={() => toggleAvailability(item._id)}
                    className={`p-2 rounded-full ${
                      item.isAvailable
                        ? 'bg-green-100 text-green-600'
                        : 'bg-red-100 text-red-600'
                    }`}
                  >
                    {item.isAvailable ? (
                      <EyeIcon className="h-4 w-4" />
                    ) : (
                      <EyeSlashIcon className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Item Details */}
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.isAvailable
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {item.isAvailable ? 'Available' : 'Unavailable'}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{item.description}</p>

                <div className="flex justify-between items-center mb-3">
                  <span className="text-xl font-bold text-orange-600">{item.price} XAF</span>
                  <span className="text-sm text-gray-500">
                    {item.preparationTime} mins
                  </span>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(item)}
                    className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium"
                  >
                    <PencilIcon className="h-4 w-4 mr-1" />
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(item._id)}
                    className="flex-1 flex items-center justify-center px-3 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors text-sm font-medium"
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <PhotoIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No menu items found
            </h3>
            <p className="text-gray-600 mb-6">
              {selectedCategory === 'all'
                ? "You haven't added any menu items yet."
                : `No items found in the "${selectedCategory}" category.`}
            </p>
            <button
              onClick={() => setIsAddingItem(true)}
              className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Add Your First Menu Item
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MenuManagement;
