import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  CalendarDaysIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PencilIcon,
  EyeIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

const SpecialOrderManagement = () => {
  const [specialOrders, setSpecialOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [isAddingOrder, setIsAddingOrder] = useState(false);
  const [editingOrder, setEditingOrder] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm();

  // Mock special orders data
  useEffect(() => {
    const mockSpecialOrders = [
      {
        id: '1',
        orderNumber: 'SPL001',
        customerName: 'Corporate Events Ltd',
        contactPerson: '<PERSON>',
        email: '<EMAIL>',
        phone: '+237123456789',
        eventType: 'corporate',
        eventDate: '2024-02-15T18:00:00Z',
        guestCount: 150,
        status: 'confirmed',
        totalAmount: 450000,
        deposit: 135000,
        balance: 315000,
        requirements: 'Vegetarian options needed, formal presentation',
        menuItems: [
          { name: 'Fried Rice with Chicken', quantity: 100, unitPrice: 2500 },
          { name: 'Grilled Fish', quantity: 75, unitPrice: 4000 },
          { name: 'Vegetarian Fufu', quantity: 50, unitPrice: 2000 }
        ],
        createdAt: '2024-01-20T10:00:00Z',
        notes: 'Client prefers delivery at 5:30 PM'
      },
      {
        id: '2',
        orderNumber: 'SPL002',
        customerName: 'Wedding Bells',
        contactPerson: 'Mike & Lisa Thompson',
        email: '<EMAIL>',
        phone: '+237987654321',
        eventType: 'wedding',
        eventDate: '2024-02-20T14:00:00Z',
        guestCount: 200,
        status: 'pending',
        totalAmount: 600000,
        deposit: 0,
        balance: 600000,
        requirements: 'Traditional Cameroonian cuisine, outdoor setup',
        menuItems: [
          { name: 'Water Fufu and Eru', quantity: 150, unitPrice: 3000 },
          { name: 'Grilled Fish', quantity: 100, unitPrice: 4000 },
          { name: 'Fried Plantains', quantity: 200, unitPrice: 1500 }
        ],
        createdAt: '2024-01-18T14:30:00Z',
        notes: 'Need confirmation by January 25th'
      },
      {
        id: '3',
        orderNumber: 'SPL003',
        customerName: 'Birthday Celebrations',
        contactPerson: 'John Doe',
        email: '<EMAIL>',
        phone: '+237555123456',
        eventType: 'birthday',
        eventDate: '2024-01-25T16:00:00Z',
        guestCount: 50,
        status: 'completed',
        totalAmount: 125000,
        deposit: 125000,
        balance: 0,
        requirements: 'Kids-friendly options, birthday cake',
        menuItems: [
          { name: 'Fried Rice with Chicken', quantity: 40, unitPrice: 2500 },
          { name: 'Fried Plantains', quantity: 50, unitPrice: 1500 }
        ],
        createdAt: '2024-01-15T09:00:00Z',
        completedAt: '2024-01-25T18:00:00Z',
        notes: 'Event was successful, client very satisfied'
      }
    ];
    setSpecialOrders(mockSpecialOrders);
    setFilteredOrders(mockSpecialOrders);
  }, []);

  // Filter orders
  useEffect(() => {
    let filtered = specialOrders;

    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.contactPerson.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  }, [specialOrders, statusFilter, searchTerm]);

  const onSubmit = async (data) => {
    try {
      if (editingOrder) {
        // Update existing order
        setSpecialOrders(specialOrders.map(order =>
          order.id === editingOrder.id
            ? {
                ...order,
                ...data,
                totalAmount: parseFloat(data.totalAmount),
                deposit: parseFloat(data.deposit || 0),
                balance: parseFloat(data.totalAmount) - parseFloat(data.deposit || 0),
                guestCount: parseInt(data.guestCount)
              }
            : order
        ));
        toast.success('Special order updated successfully!');
        setEditingOrder(null);
      } else {
        // Add new order
        const newOrder = {
          id: Date.now().toString(),
          orderNumber: `SPL${String(specialOrders.length + 1).padStart(3, '0')}`,
          ...data,
          totalAmount: parseFloat(data.totalAmount),
          deposit: parseFloat(data.deposit || 0),
          balance: parseFloat(data.totalAmount) - parseFloat(data.deposit || 0),
          guestCount: parseInt(data.guestCount),
          status: 'pending',
          menuItems: [],
          createdAt: new Date().toISOString()
        };
        setSpecialOrders([...specialOrders, newOrder]);
        toast.success('Special order added successfully!');
        setIsAddingOrder(false);
      }
      reset();
    } catch (error) {
      toast.error('Failed to save special order');
    }
  };

  const handleEdit = (order) => {
    setEditingOrder(order);
    setIsAddingOrder(true);
    setValue('customerName', order.customerName);
    setValue('contactPerson', order.contactPerson);
    setValue('email', order.email);
    setValue('phone', order.phone);
    setValue('eventType', order.eventType);
    setValue('eventDate', order.eventDate.slice(0, 16));
    setValue('guestCount', order.guestCount);
    setValue('totalAmount', order.totalAmount);
    setValue('deposit', order.deposit);
    setValue('requirements', order.requirements);
    setValue('notes', order.notes);
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    setSpecialOrders(specialOrders.map(order =>
      order.id === orderId
        ? {
            ...order,
            status: newStatus,
            ...(newStatus === 'completed' && { completedAt: new Date().toISOString() })
          }
        : order
    ));
    toast.success(`Order status updated to ${newStatus}`);
  };

  const handleCancel = () => {
    setIsAddingOrder(false);
    setEditingOrder(null);
    reset();
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US').format(amount);
  };

  const eventTypes = [
    { value: 'wedding', label: 'Wedding' },
    { value: 'corporate', label: 'Corporate Event' },
    { value: 'birthday', label: 'Birthday Party' },
    { value: 'conference', label: 'Conference' },
    { value: 'graduation', label: 'Graduation' },
    { value: 'other', label: 'Other' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Orders' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Special Order Management</h1>
            <p className="text-gray-600 mt-2">Manage event catering and bulk orders</p>
          </div>
          <button
            onClick={() => setIsAddingOrder(true)}
            className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Special Order
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{specialOrders.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {specialOrders.filter(o => o.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <UsersIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Guests</p>
                <p className="text-2xl font-bold text-gray-900">
                  {specialOrders.reduce((sum, order) => sum + order.guestCount, 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(specialOrders.reduce((sum, order) => sum + order.totalAmount, 0))} XAF
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search orders..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Add/Edit Form */}
        {isAddingOrder && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-6 mb-6"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {editingOrder ? 'Edit Special Order' : 'Add New Special Order'}
            </h2>
            <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer/Organization Name *
                </label>
                <input
                  {...register('customerName', { required: 'Customer name is required' })}
                  type="text"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter customer name"
                />
                {errors.customerName && (
                  <p className="text-red-500 text-sm mt-1">{errors.customerName.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Person *
                </label>
                <input
                  {...register('contactPerson', { required: 'Contact person is required' })}
                  type="text"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter contact person name"
                />
                {errors.contactPerson && (
                  <p className="text-red-500 text-sm mt-1">{errors.contactPerson.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Please enter a valid email'
                    }
                  })}
                  type="email"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter email address"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  {...register('phone', { required: 'Phone number is required' })}
                  type="tel"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter phone number"
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Type *
                </label>
                <select
                  {...register('eventType', { required: 'Event type is required' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="">Select event type</option>
                  {eventTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.eventType && (
                  <p className="text-red-500 text-sm mt-1">{errors.eventType.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Date & Time *
                </label>
                <input
                  {...register('eventDate', { required: 'Event date is required' })}
                  type="datetime-local"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                {errors.eventDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.eventDate.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Guest Count *
                </label>
                <input
                  {...register('guestCount', {
                    required: 'Guest count is required',
                    min: { value: 1, message: 'Guest count must be at least 1' }
                  })}
                  type="number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter number of guests"
                />
                {errors.guestCount && (
                  <p className="text-red-500 text-sm mt-1">{errors.guestCount.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total Amount (XAF) *
                </label>
                <input
                  {...register('totalAmount', {
                    required: 'Total amount is required',
                    min: { value: 0, message: 'Amount must be positive' }
                  })}
                  type="number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter total amount"
                />
                {errors.totalAmount && (
                  <p className="text-red-500 text-sm mt-1">{errors.totalAmount.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deposit Amount (XAF)
                </label>
                <input
                  {...register('deposit')}
                  type="number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter deposit amount"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Special Requirements
                </label>
                <textarea
                  {...register('requirements')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter any special requirements..."
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  {...register('notes')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter any additional notes..."
                />
              </div>

              <div className="md:col-span-2 flex space-x-4">
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                >
                  {editingOrder ? 'Update Order' : 'Add Order'}
                </motion.button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </form>
          </motion.div>
        )}

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
            >
              <div className="p-6">
                {/* Order Header */}
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                  <div className="flex items-center space-x-3 mb-2 lg:mb-0">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {order.orderNumber} - {order.customerName}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {order.contactPerson} • {formatDate(order.eventDate)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                    <span className="text-sm text-gray-500 capitalize">
                      {order.eventType}
                    </span>
                  </div>
                </div>

                {/* Order Details */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Guests</p>
                    <div className="flex items-center">
                      <UsersIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="font-medium">{order.guestCount}</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Total Amount</p>
                    <p className="font-bold text-orange-600">{formatCurrency(order.totalAmount)} XAF</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Deposit</p>
                    <p className="font-medium text-green-600">{formatCurrency(order.deposit)} XAF</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Balance</p>
                    <p className="font-medium text-red-600">{formatCurrency(order.balance)} XAF</p>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 mb-4">
                  <div className="flex items-center">
                    <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">{order.phone}</span>
                  </div>
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">{order.email}</span>
                  </div>
                </div>

                {/* Requirements */}
                {order.requirements && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Special Requirements</p>
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{order.requirements}</p>
                  </div>
                )}

                {/* Menu Items */}
                {order.menuItems && order.menuItems.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-2">Menu Items</p>
                    <div className="space-y-1">
                      {order.menuItems.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex justify-between text-sm">
                          <span className="text-gray-600">{item.name} x{item.quantity}</span>
                          <span className="font-medium">{formatCurrency(item.unitPrice * item.quantity)} XAF</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {order.notes && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Notes</p>
                    <p className="text-sm text-gray-700 italic">{order.notes}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                  {order.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'confirmed')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        Confirm Order
                      </button>
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'cancelled')}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                      >
                        Cancel Order
                      </button>
                    </>
                  )}

                  {order.status === 'confirmed' && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'completed')}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                    >
                      Mark Completed
                    </button>
                  )}

                  <button
                    onClick={() => handleEdit(order)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                  >
                    <PencilIcon className="h-4 w-4 inline mr-1" />
                    Edit
                  </button>

                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                    <EyeIcon className="h-4 w-4 inline mr-1" />
                    View Details
                  </button>

                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                    Contact Customer
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <CalendarDaysIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No special orders found
            </h3>
            <p className="text-gray-600 mb-6">
              {statusFilter === 'all'
                ? "No special orders have been created yet."
                : `No orders with status "${statusFilter}" found.`}
            </p>
            <button
              onClick={() => setIsAddingOrder(true)}
              className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Add Your First Special Order
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SpecialOrderManagement;
