import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  PhoneIcon,
  MapPinIcon,
  CreditCardIcon,
  ReceiptPercentIcon
} from '@heroicons/react/24/outline';
import { friedRice, friedPlantains } from '../assets';

const OrderDetail = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);

  // Mock order data
  useEffect(() => {
    // In real app, fetch order details from API
    const mockOrder = {
      id: id,
      orderNumber: `ZCH${id}`,
      status: 'delivered',
      paymentStatus: 'paid',
      paymentMethod: 'mtn-mobile-money',
      totalAmount: 7500,
      deliveryFee: 500,
      subtotal: 7000,
      createdAt: '2024-01-20T10:30:00Z',
      estimatedDeliveryTime: '2024-01-20T11:00:00Z',
      deliveredAt: '2024-01-20T10:55:00Z',
      customerInfo: {
        name: 'John Doe',
        phone: '+237123456789',
        email: '<EMAIL>'
      },
      deliveryInfo: {
        type: 'delivery',
        address: {
          street: '123 Main St',
          city: 'Douala',
          instructions: 'Call when you arrive at the gate'
        }
      },
      items: [
        {
          id: '1',
          menuItem: {
            _id: '1',
            name: 'Fried Rice with Chicken',
            images: [{ url: friedRice }]
          },
          quantity: 2,
          price: 2500,
          specialInstructions: 'Extra spicy'
        },
        {
          id: '2',
          menuItem: {
            _id: '2',
            name: 'Fried Plantains',
            images: [{ url: friedPlantains }]
          },
          quantity: 1,
          price: 1500,
          specialInstructions: ''
        }
      ],
      deliveryPartner: {
        name: 'Mike Johnson',
        phone: '+237555123456',
        rating: 4.8
      },
      timeline: [
        {
          status: 'pending',
          timestamp: '2024-01-20T10:30:00Z',
          description: 'Order placed'
        },
        {
          status: 'confirmed',
          timestamp: '2024-01-20T10:32:00Z',
          description: 'Order confirmed by restaurant'
        },
        {
          status: 'preparing',
          timestamp: '2024-01-20T10:35:00Z',
          description: 'Kitchen started preparing your order'
        },
        {
          status: 'ready',
          timestamp: '2024-01-20T10:45:00Z',
          description: 'Order ready for pickup'
        },
        {
          status: 'out-for-delivery',
          timestamp: '2024-01-20T10:48:00Z',
          description: 'Order picked up by delivery partner'
        },
        {
          status: 'delivered',
          timestamp: '2024-01-20T10:55:00Z',
          description: 'Order delivered successfully'
        }
      ]
    };
    setOrder(mockOrder);
  }, [id]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
      case 'confirmed':
      case 'preparing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'ready':
      case 'out-for-delivery':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'preparing':
        return 'bg-orange-100 text-orange-800';
      case 'ready':
        return 'bg-purple-100 text-purple-800';
      case 'out-for-delivery':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US').format(amount);
  };

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading order details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate('/orders')}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors mr-4"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-1" />
            Back to Orders
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Order #{order.orderNumber}
            </h1>
            <p className="text-gray-600 mt-1">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Status */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Order Status</h2>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(order.status)}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('-', ' ')}
                  </span>
                </div>
              </div>

              {order.status === 'delivered' && order.deliveredAt && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-green-800 font-medium">
                      Delivered on {formatDate(order.deliveredAt)}
                    </span>
                  </div>
                </div>
              )}

              {/* Timeline */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-700">Order Timeline</h3>
                <div className="space-y-3">
                  {order.timeline.map((event, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                        <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{event.description}</p>
                        <p className="text-xs text-gray-500">{formatTime(event.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Items</h2>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                    <img
                      src={item.menuItem.images[0]?.url || friedRice}
                      alt={item.menuItem.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.menuItem.name}</h3>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                      {item.specialInstructions && (
                        <p className="text-sm text-gray-500 italic">
                          Note: {item.specialInstructions}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(item.price * item.quantity)} XAF
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatCurrency(item.price)} XAF each
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Delivery Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Delivery Information</h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Delivery Type</p>
                  <p className="font-medium">
                    {order.deliveryInfo.type === 'delivery' ? 'Home Delivery' : 'Pickup'}
                  </p>
                </div>

                {order.deliveryInfo.type === 'delivery' && (
                  <div>
                    <p className="text-sm text-gray-500">Delivery Address</p>
                    <div className="flex items-start mt-1">
                      <MapPinIcon className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">{order.deliveryInfo.address.street}</p>
                        <p className="text-gray-600">{order.deliveryInfo.address.city}</p>
                        {order.deliveryInfo.address.instructions && (
                          <p className="text-sm text-gray-500 italic mt-1">
                            Instructions: {order.deliveryInfo.address.instructions}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {order.deliveryPartner && (
                  <div>
                    <p className="text-sm text-gray-500">Delivery Partner</p>
                    <div className="flex items-center mt-1">
                      <div className="flex-1">
                        <p className="font-medium">{order.deliveryPartner.name}</p>
                        <p className="text-sm text-gray-600">⭐ {order.deliveryPartner.rating} rating</p>
                      </div>
                      <button className="flex items-center px-3 py-1 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                        <PhoneIcon className="h-4 w-4 mr-1" />
                        Call
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Summary</h2>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">{formatCurrency(order.subtotal)} XAF</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee</span>
                  <span className="font-medium">{formatCurrency(order.deliveryFee)} XAF</span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-orange-600">
                      {formatCurrency(order.totalAmount)} XAF
                    </span>
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="font-medium text-gray-700 mb-3">Payment Information</h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <CreditCardIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">
                      {order.paymentMethod.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      order.paymentStatus === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.paymentStatus === 'paid' ? 'Paid' : 'Pending'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="border-t border-gray-200 pt-4 mt-4 space-y-3">
                {order.status === 'delivered' && (
                  <Link
                    to={`/feedback/${order.id}`}
                    className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors text-center block font-medium"
                  >
                    Leave Feedback
                  </Link>
                )}

                <button className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                  <ReceiptPercentIcon className="h-4 w-4 inline mr-2" />
                  Download Receipt
                </button>

                <button className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                  Reorder Items
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;
