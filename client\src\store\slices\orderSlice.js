import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  orders: [],
  currentOrder: null,
  specialOrders: [],
  currentSpecialOrder: null,
  isLoading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
  filters: {
    status: '',
    dateRange: null,
  },
};

const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setOrders: (state, action) => {
      state.orders = action.payload.orders;
      state.pagination = action.payload.pagination;
      state.isLoading = false;
      state.error = null;
    },
    setCurrentOrder: (state, action) => {
      state.currentOrder = action.payload;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    addOrder: (state, action) => {
      state.orders.unshift(action.payload);
    },
    updateOrder: (state, action) => {
      const updatedOrder = action.payload;
      const index = state.orders.findIndex(order => order._id === updatedOrder._id);
      if (index !== -1) {
        state.orders[index] = updatedOrder;
      }
      if (state.currentOrder && state.currentOrder._id === updatedOrder._id) {
        state.currentOrder = updatedOrder;
      }
    },
    setSpecialOrders: (state, action) => {
      state.specialOrders = action.payload.specialOrders;
      state.pagination = action.payload.pagination;
      state.isLoading = false;
      state.error = null;
    },
    setCurrentSpecialOrder: (state, action) => {
      state.currentSpecialOrder = action.payload;
    },
    clearCurrentSpecialOrder: (state) => {
      state.currentSpecialOrder = null;
    },
    addSpecialOrder: (state, action) => {
      state.specialOrders.unshift(action.payload);
    },
    updateSpecialOrder: (state, action) => {
      const updatedOrder = action.payload;
      const index = state.specialOrders.findIndex(order => order._id === updatedOrder._id);
      if (index !== -1) {
        state.specialOrders[index] = updatedOrder;
      }
      if (state.currentSpecialOrder && state.currentSpecialOrder._id === updatedOrder._id) {
        state.currentSpecialOrder = updatedOrder;
      }
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        status: '',
        dateRange: null,
      };
    },
  },
});

export const {
  setLoading,
  setError,
  clearError,
  setOrders,
  setCurrentOrder,
  clearCurrentOrder,
  addOrder,
  updateOrder,
  setSpecialOrders,
  setCurrentSpecialOrder,
  clearCurrentSpecialOrder,
  addSpecialOrder,
  updateSpecialOrder,
  setFilters,
  clearFilters,
} = orderSlice.actions;

export default orderSlice.reducer;
