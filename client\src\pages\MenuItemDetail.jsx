import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import {
  ArrowLeftIcon,
  ClockIcon,
  StarIcon,
  PlusIcon,
  MinusIcon,
  ShoppingCartIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { addToCart } from '../store/slices/cartSlice';

const MenuItemDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [menuItem, setMenuItem] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  // Mock menu item data with your uploaded images
  useEffect(() => {
    // In real app, fetch menu item details from API
    const mockMenuItem = {
      _id: id,
      name: 'Fried Rice with Chicken',
      description: 'Delicious fried rice with tender chicken pieces, mixed vegetables, and aromatic spices. Served hot and fresh with a side of plantains.',
      price: 2500,
      category: 'rice-dishes',
      images: [
        { url: '/src/assets/images/fried-rice.jpg' },
        { url: '/src/assets/images/fried-rice-2.jpg' },
        { url: '/src/assets/images/fried-rice-3.jpg' }
      ],
      isAvailable: true,
      preparationTime: 15,
      ingredients: ['Jasmine Rice', 'Chicken Breast', 'Mixed Vegetables', 'Soy Sauce', 'Garlic', 'Ginger', 'Spring Onions'],
      nutritionalInfo: {
        calories: 450,
        protein: 25,
        carbs: 55,
        fat: 12,
        fiber: 3
      },
      allergens: ['Soy', 'Gluten'],
      spiceLevel: 'Medium',
      rating: 4.5,
      reviewCount: 128,
      reviews: [
        {
          id: '1',
          customerName: 'Sarah M.',
          rating: 5,
          comment: 'Amazing taste! The chicken was perfectly cooked and the rice was flavorful.',
          date: '2024-01-18'
        },
        {
          id: '2',
          customerName: 'John D.',
          rating: 4,
          comment: 'Good portion size and delicious. Will order again!',
          date: '2024-01-17'
        },
        {
          id: '3',
          customerName: 'Mary K.',
          rating: 5,
          comment: 'Best fried rice in Douala! Fast delivery too.',
          date: '2024-01-16'
        }
      ]
    };
    setMenuItem(mockMenuItem);
  }, [id]);

  const handleAddToCart = () => {
    if (!menuItem) return;

    const cartItem = {
      menuItem,
      quantity,
      price: menuItem.price,
      specialInstructions: specialInstructions.trim()
    };

    dispatch(addToCart(cartItem));
    toast.success(`${quantity} x ${menuItem.name} added to cart!`);
  };

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  if (!menuItem) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading menu item...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={() => navigate('/menu')}
          className="flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-6"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Menu
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Gallery */}
          <div className="space-y-4">
            <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
              <img
                src={menuItem.images[selectedImage]?.url || '/src/assets/images/placeholder-food.jpg'}
                alt={menuItem.name}
                className="w-full h-96 object-cover"
              />
            </div>

            {menuItem.images.length > 1 && (
              <div className="flex space-x-2">
                {menuItem.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-orange-500' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={`${menuItem.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Item Details */}
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-start mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{menuItem.name}</h1>
                <button
                  onClick={toggleFavorite}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                >
                  {isFavorite ? (
                    <HeartIconSolid className="h-6 w-6 text-red-500" />
                  ) : (
                    <HeartIcon className="h-6 w-6 text-gray-400" />
                  )}
                </button>
              </div>

              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center">
                  <StarIcon className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm font-medium">{menuItem.rating}</span>
                  <span className="ml-1 text-sm text-gray-500">({menuItem.reviewCount} reviews)</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {menuItem.preparationTime} mins
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  menuItem.isAvailable
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {menuItem.isAvailable ? 'Available' : 'Unavailable'}
                </span>
              </div>

              <p className="text-gray-600 text-lg leading-relaxed">{menuItem.description}</p>
            </div>

            {/* Price */}
            <div className="text-3xl font-bold text-orange-600">
              {menuItem.price} XAF
            </div>

            {/* Quantity Selector */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                    className="p-2 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <MinusIcon className="h-4 w-4" />
                  </button>
                  <span className="text-xl font-semibold w-12 text-center">{quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= 10}
                    className="p-2 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <PlusIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Special Instructions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Special Instructions (Optional)
                </label>
                <textarea
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Any special requests for this item..."
                />
              </div>
            </div>

            {/* Add to Cart Button */}
            <motion.button
              onClick={handleAddToCart}
              disabled={!menuItem.isAvailable}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-orange-600 text-white py-4 px-6 rounded-lg hover:bg-orange-700 transition-colors font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <ShoppingCartIcon className="h-6 w-6 mr-2" />
              Add to Cart - {(menuItem.price * quantity).toLocaleString()} XAF
            </motion.button>

            {/* Total */}
            <div className="text-center text-gray-600">
              Total: {(menuItem.price * quantity).toLocaleString()} XAF
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Ingredients & Nutrition */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Ingredients & Nutrition</h2>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Ingredients</h3>
                <div className="flex flex-wrap gap-2">
                  {menuItem.ingredients.map((ingredient, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-700 mb-2">Nutritional Information (per serving)</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span>Calories:</span>
                    <span className="font-medium">{menuItem.nutritionalInfo.calories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Protein:</span>
                    <span className="font-medium">{menuItem.nutritionalInfo.protein}g</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Carbs:</span>
                    <span className="font-medium">{menuItem.nutritionalInfo.carbs}g</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fat:</span>
                    <span className="font-medium">{menuItem.nutritionalInfo.fat}g</span>
                  </div>
                </div>
              </div>

              {menuItem.allergens && menuItem.allergens.length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-700 mb-2">Allergens</h3>
                  <div className="flex flex-wrap gap-2">
                    {menuItem.allergens.map((allergen, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm"
                      >
                        {allergen}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <h3 className="font-medium text-gray-700 mb-2">Spice Level</h3>
                <span className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm">
                  {menuItem.spiceLevel}
                </span>
              </div>
            </div>
          </div>

          {/* Reviews */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Customer Reviews</h2>

            <div className="space-y-4">
              {menuItem.reviews.map((review) => (
                <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900">{review.customerName}</span>
                      <div className="flex ml-2">
                        {[...Array(5)].map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <span className="text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-600 text-sm">{review.comment}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuItemDetail;
