{"hash": "6eb12a22", "configHash": "d00de2df", "lockfileHash": "169ef847", "browserHash": "cf21481f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d247917c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "ffaab100", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1e4c9d7a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "14b30cd3", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "9b1a7d21", "needsInterop": true}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "0aa1b70b", "needsInterop": false}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "988b9c08", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "68616064", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "5326270b", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "af73a9b1", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "648113f3", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "e62d291f", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "362d4918", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "22967d08", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "f1acb47c", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "ea0f3700", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "899147ee", "needsInterop": false}}, "chunks": {"chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}