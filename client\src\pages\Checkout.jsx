import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { clearCart } from '../store/slices/cartSlice';
import { addOrder } from '../store/slices/orderSlice';

const Checkout = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { items, totalAmount } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, formState: { errors }, watch } = useForm({
    defaultValues: {
      deliveryType: 'delivery',
      paymentMethod: 'cash-on-delivery',
      customerName: user?.name || '',
      customerPhone: user?.phone || '',
      customerEmail: user?.email || ''
    }
  });

  const deliveryType = watch('deliveryType');
  const deliveryFee = totalAmount >= 5000 ? 0 : 500;
  const finalTotal = totalAmount + deliveryFee;

  const onSubmit = async (data) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create order object
      const order = {
        id: Date.now().toString(),
        orderNumber: `ZCH${Date.now()}`,
        items: items,
        totalAmount: finalTotal,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: data.paymentMethod,
        deliveryInfo: {
          type: data.deliveryType,
          address: data.deliveryType === 'delivery' ? {
            street: data.street,
            city: data.city,
            instructions: data.deliveryInstructions
          } : null,
          scheduledTime: data.scheduledTime || null
        },
        customerInfo: {
          name: data.customerName,
          phone: data.customerPhone,
          email: data.customerEmail
        },
        customerNotes: data.customerNotes,
        createdAt: new Date().toISOString()
      };

      // Add order to store
      dispatch(addOrder(order));

      // Clear cart
      dispatch(clearCart());

      toast.success(t('orderPlacedSuccess'));

      // Redirect to order confirmation
      navigate(`/orders/${order.id}`);
    } catch (error) {
      toast.error('Failed to place order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (items.length === 0) {
    navigate('/cart');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          {t('checkout')}
        </h1>

        <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('contactInfo')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('name')} *
                  </label>
                  <input
                    {...register('customerName', { required: 'Name is required' })}
                    type="text"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                  {errors.customerName && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerName.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('phoneNumber')} *
                  </label>
                  <input
                    {...register('customerPhone', { required: 'Phone number is required' })}
                    type="tel"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                  {errors.customerPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerPhone.message}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('emailAddress')}
                  </label>
                  <input
                    {...register('customerEmail')}
                    type="email"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Delivery Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('deliveryInfo')}
              </h2>

              {/* Delivery Type */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delivery Type *
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      {...register('deliveryType')}
                      type="radio"
                      value="delivery"
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm font-medium text-gray-900">
                      {t('delivery')}
                    </span>
                  </label>
                  <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      {...register('deliveryType')}
                      type="radio"
                      value="pickup"
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm font-medium text-gray-900">
                      {t('pickup')}
                    </span>
                  </label>
                </div>
              </div>

              {/* Delivery Address (only if delivery is selected) */}
              {deliveryType === 'delivery' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Street Address *
                    </label>
                    <input
                      {...register('street', {
                        required: deliveryType === 'delivery' ? 'Street address is required' : false
                      })}
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Enter your street address"
                    />
                    {errors.street && (
                      <p className="text-red-500 text-sm mt-1">{errors.street.message}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City *
                    </label>
                    <input
                      {...register('city', {
                        required: deliveryType === 'delivery' ? 'City is required' : false
                      })}
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Douala"
                    />
                    {errors.city && (
                      <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Instructions
                    </label>
                    <textarea
                      {...register('deliveryInstructions')}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Any special instructions for delivery..."
                    />
                  </div>
                </div>
              )}

              {/* Scheduled Time */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Time (Optional)
                </label>
                <input
                  {...register('scheduledTime')}
                  type="datetime-local"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('paymentMethod')}
              </h2>
              <div className="space-y-3">
                <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    {...register('paymentMethod')}
                    type="radio"
                    value="cash-on-delivery"
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    {t('cashOnDelivery')}
                  </span>
                </label>
                <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    {...register('paymentMethod')}
                    type="radio"
                    value="mtn-mobile-money"
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    {t('mtnMobileMoney')}
                  </span>
                </label>
                <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    {...register('paymentMethod')}
                    type="radio"
                    value="orange-money"
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    {t('orangeMoney')}
                  </span>
                </label>
              </div>
            </div>

            {/* Additional Notes */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Additional Notes
              </h2>
              <textarea
                {...register('customerNotes')}
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Any special requests or notes for your order..."
              />
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('orderSummary')}
              </h2>

              {/* Order Items */}
              <div className="space-y-3 mb-6">
                {items.map((item) => (
                  <div key={`${item.menuItem._id}-${item.specialInstructions}`} className="flex justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {item.menuItem.name} x{item.quantity}
                      </p>
                      {item.specialInstructions && (
                        <p className="text-xs text-gray-500">
                          Note: {item.specialInstructions}
                        </p>
                      )}
                    </div>
                    <p className="text-sm font-semibold text-gray-900">
                      {item.price * item.quantity} XAF
                    </p>
                  </div>
                ))}
              </div>

              {/* Totals */}
              <div className="space-y-3 mb-6 border-t border-gray-200 pt-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-semibold">{totalAmount} XAF</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee</span>
                  <span className="font-semibold">
                    {deliveryFee === 0 ? 'Free' : `${deliveryFee} XAF`}
                  </span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-orange-600">
                      {finalTotal} XAF
                    </span>
                  </div>
                </div>
              </div>

              {/* Place Order Button */}
              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Placing Order...
                  </div>
                ) : (
                  `Place Order - ${finalTotal} XAF`
                )}
              </motion.button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Checkout;
