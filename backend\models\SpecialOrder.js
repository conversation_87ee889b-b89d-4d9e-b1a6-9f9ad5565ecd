import mongoose from 'mongoose';

const specialOrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  type: {
    type: String,
    enum: ['event', 'bulk'],
    required: true
  },
  customer: {
    name: {
      type: String,
      required: [true, 'Customer name is required'],
      trim: true
    },
    phone: {
      type: String,
      required: [true, 'Phone number is required'],
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    }
  },
  eventDetails: {
    eventName: {
      type: String,
      required: function() {
        return this.type === 'event';
      }
    },
    eventDate: {
      type: Date,
      required: function() {
        return this.type === 'event';
      }
    },
    eventTime: {
      type: String,
      required: function() {
        return this.type === 'event';
      }
    },
    numberOfPeople: {
      type: Number,
      required: function() {
        return this.type === 'event';
      },
      min: [1, 'Number of people must be at least 1']
    },
    venue: {
      name: String,
      address: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    }
  },
  bulkDetails: {
    mealType: {
      type: String,
      required: function() {
        return this.type === 'bulk';
      }
    },
    servings: {
      type: Number,
      required: function() {
        return this.type === 'bulk';
      },
      min: [1, 'Servings must be at least 1']
    },
    deliveryDay: {
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
      required: function() {
        return this.type === 'bulk';
      }
    },
    frequency: {
      type: String,
      enum: ['one-time', 'weekly', 'bi-weekly', 'monthly'],
      default: 'one-time'
    }
  },
  selectedItems: [{
    menuItem: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MenuItem'
    },
    customItem: {
      name: String,
      description: String
    },
    quantity: {
      type: Number,
      required: true,
      min: [1, 'Quantity must be at least 1']
    },
    specialInstructions: String
  }],
  deliveryLocation: {
    address: {
      type: String,
      required: [true, 'Delivery address is required']
    },
    city: String,
    state: String,
    country: {
      type: String,
      default: 'Cameroon'
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    instructions: String
  },
  budget: {
    min: Number,
    max: Number,
    currency: {
      type: String,
      default: 'XAF'
    }
  },
  specialRequests: String,
  dietaryRestrictions: [String],
  callToConfirm: {
    type: Boolean,
    default: false
  },
  preferredContactMethod: {
    type: String,
    enum: ['phone', 'email', 'whatsapp'],
    default: 'phone'
  },
  status: {
    type: String,
    enum: [
      'submitted',
      'reviewed',
      'quoted',
      'confirmed',
      'in-preparation',
      'ready',
      'delivered',
      'cancelled',
      'completed'
    ],
    default: 'submitted'
  },
  quotedPrice: Number,
  finalPrice: Number,
  paymentStatus: {
    type: String,
    enum: ['pending', 'partial', 'paid', 'refunded'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'mtn-mobile-money', 'orange-money', 'bank-transfer', 'card']
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  internalNotes: String,
  customerNotes: String,
  statusHistory: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  communicationLog: [{
    method: {
      type: String,
      enum: ['email', 'phone', 'whatsapp', 'in-person']
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    summary: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    handledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  attachments: [{
    filename: String,
    url: String,
    type: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes for better query performance
specialOrderSchema.index({ orderNumber: 1 });
specialOrderSchema.index({ type: 1 });
specialOrderSchema.index({ status: 1 });
specialOrderSchema.index({ 'customer.phone': 1 });
specialOrderSchema.index({ 'eventDetails.eventDate': 1 });
specialOrderSchema.index({ createdAt: -1 });

// Pre-save middleware to generate order number
specialOrderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const typePrefix = this.type === 'event' ? 'EVT' : 'BLK';
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.orderNumber = `ZCH${typePrefix}${dateStr}${randomNum}`;
  }
  next();
});

// Method to add status update
specialOrderSchema.methods.updateStatus = function(newStatus, updatedBy, notes = '') {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    updatedBy,
    notes
  });
  return this.save();
};

// Method to add communication log entry
specialOrderSchema.methods.addCommunication = function(method, direction, summary, handledBy) {
  this.communicationLog.push({
    method,
    direction,
    summary,
    handledBy
  });
  return this.save();
};

export default mongoose.model('SpecialOrder', specialOrderSchema);
