import express from 'express';
import { body, query, validationResult } from 'express-validator';
import Order from '../models/Order.js';
import MenuItem from '../models/MenuItem.js';
import { authenticate, authorize } from '../middleware/auth.js';

const router = express.Router();

// Validation rules
const orderValidation = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.menuItem')
    .isMongoId()
    .withMessage('Invalid menu item ID'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('deliveryInfo.type')
    .isIn(['delivery', 'pickup'])
    .withMessage('Delivery type must be delivery or pickup'),
  body('deliveryInfo.address.street')
    .if(body('deliveryInfo.type').equals('delivery'))
    .notEmpty()
    .withMessage('Street address is required for delivery'),
  body('paymentMethod')
    .optional()
    .isIn(['cash-on-delivery', 'mtn-mobile-money', 'orange-money', 'card'])
    .withMessage('Invalid payment method')
];

// @route   POST /api/orders
// @desc    Create new order
// @access  Private (Customer)
router.post('/', authenticate, orderValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { items, deliveryInfo, paymentMethod = 'cash-on-delivery', customerNotes } = req.body;

    // Validate menu items and calculate total
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const menuItem = await MenuItem.findOne({
        _id: item.menuItem,
        isActive: true,
        'availability.isAvailable': true
      });

      if (!menuItem) {
        return res.status(400).json({
          success: false,
          message: `Menu item ${item.menuItem} is not available`
        });
      }

      const itemTotal = menuItem.price * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        menuItem: menuItem._id,
        quantity: item.quantity,
        price: menuItem.price,
        specialInstructions: item.specialInstructions
      });

      // Update order count for menu item
      menuItem.orderCount += item.quantity;
      await menuItem.save();
    }

    // Create order
    const order = new Order({
      customer: req.user.id,
      items: orderItems,
      totalAmount,
      deliveryInfo,
      paymentMethod,
      customerNotes
    });

    // Calculate estimated delivery time
    await order.calculateEstimatedDeliveryTime();
    await order.save();

    // Populate order details
    await order.populate([
      { path: 'customer', select: 'name phone email' },
      { path: 'items.menuItem', select: 'name price images' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Order placed successfully',
      data: { order }
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/orders
// @desc    Get orders (filtered by user role)
// @access  Private
router.get('/', authenticate, [
  query('status').optional().isIn(['pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered', 'cancelled']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const { status, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query based on user role
    let query = {};
    
    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'delivery') {
      query.assignedDeliveryPartner = req.user.id;
    }
    // Owners can see all orders (no additional filter)

    if (status) query.status = status;

    const orders = await Order.find(query)
      .populate([
        { path: 'customer', select: 'name phone email' },
        { path: 'items.menuItem', select: 'name price images' },
        { path: 'assignedDeliveryPartner', select: 'name phone' }
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/orders/:id
// @desc    Get single order
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    let query = { _id: req.params.id };

    // Customers can only see their own orders
    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'delivery') {
      query.assignedDeliveryPartner = req.user.id;
    }

    const order = await Order.findOne(query)
      .populate([
        { path: 'customer', select: 'name phone email address' },
        { path: 'items.menuItem', select: 'name description price images' },
        { path: 'assignedDeliveryPartner', select: 'name phone' },
        { path: 'statusHistory.updatedBy', select: 'name role' }
      ]);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: { order }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/orders/:id/status
// @desc    Update order status
// @access  Private (Owner/Delivery)
router.patch('/:id/status', authenticate, authorize('owner', 'delivery'), [
  body('status')
    .isIn(['pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered', 'cancelled'])
    .withMessage('Invalid status'),
  body('notes').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes = '' } = req.body;
    
    let query = { _id: req.params.id };
    
    // Delivery partners can only update their assigned orders
    if (req.user.role === 'delivery') {
      query.assignedDeliveryPartner = req.user.id;
    }

    const order = await Order.findOne(query);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or not authorized'
      });
    }

    // Update status with history
    await order.updateStatus(status, req.user.id, notes);

    await order.populate([
      { path: 'customer', select: 'name phone email' },
      { path: 'items.menuItem', select: 'name price' },
      { path: 'assignedDeliveryPartner', select: 'name phone' }
    ]);

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: { order }
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/orders/:id/assign
// @desc    Assign delivery partner to order
// @access  Private (Owner only)
router.patch('/:id/assign', authenticate, authorize('owner'), [
  body('deliveryPartnerId').isMongoId().withMessage('Invalid delivery partner ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { deliveryPartnerId } = req.body;

    const order = await Order.findByIdAndUpdate(
      req.params.id,
      { assignedDeliveryPartner: deliveryPartnerId },
      { new: true }
    ).populate([
      { path: 'customer', select: 'name phone email' },
      { path: 'assignedDeliveryPartner', select: 'name phone' }
    ]);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      message: 'Delivery partner assigned successfully',
      data: { order }
    });
  } catch (error) {
    console.error('Assign delivery partner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign delivery partner',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
