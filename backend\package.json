{"name": "zina-chop-house-backend", "version": "1.0.0", "description": "Backend API for Zina Chop House food ordering system", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["food", "ordering", "restaurant", "api"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "cloudinary": "^1.41.3", "nodemailer": "^6.9.7", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}