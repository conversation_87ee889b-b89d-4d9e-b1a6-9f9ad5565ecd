import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  PhoneIcon,
  MapPinIcon,
  EyeIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Mock orders data
  useEffect(() => {
    const mockOrders = [
      {
        id: '1',
        orderNumber: 'ZCH1640995200000',
        customerInfo: {
          name: '<PERSON>',
          phone: '+237123456789',
          email: '<EMAIL>'
        },
        items: [
          {
            menuItem: { name: 'Fried Rice with Chicken', price: 2500 },
            quantity: 2,
            price: 2500,
            specialInstructions: 'Extra spicy'
          },
          {
            menuItem: { name: 'Fried Plantains', price: 1500 },
            quantity: 1,
            price: 1500
          }
        ],
        totalAmount: 6500,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: 'cash-on-delivery',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '123 Main St',
            city: 'Douala'
          },
          instructions: 'Call when you arrive'
        },
        createdAt: '2024-01-20T10:30:00Z',
        estimatedDeliveryTime: '2024-01-20T11:00:00Z'
      },
      {
        id: '2',
        orderNumber: 'ZCH1640995300000',
        customerInfo: {
          name: 'Jane Smith',
          phone: '+237987654321',
          email: '<EMAIL>'
        },
        items: [
          {
            menuItem: { name: 'Water Fufu and Eru', price: 3000 },
            quantity: 2,
            price: 3000
          }
        ],
        totalAmount: 6000,
        status: 'preparing',
        paymentStatus: 'paid',
        paymentMethod: 'mtn-mobile-money',
        deliveryInfo: {
          type: 'pickup'
        },
        createdAt: '2024-01-20T11:15:00Z',
        estimatedDeliveryTime: '2024-01-20T11:45:00Z'
      },
      {
        id: '3',
        orderNumber: 'ZCH1640995400000',
        customerInfo: {
          name: 'Mike Johnson',
          phone: '+237555123456',
          email: '<EMAIL>'
        },
        items: [
          {
            menuItem: { name: 'Grilled Fish', price: 4000 },
            quantity: 1,
            price: 4000
          },
          {
            menuItem: { name: 'Pepper Soup', price: 2000 },
            quantity: 1,
            price: 2000
          }
        ],
        totalAmount: 6500,
        status: 'ready',
        paymentStatus: 'paid',
        paymentMethod: 'orange-money',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '456 Oak Ave',
            city: 'Douala'
          }
        },
        createdAt: '2024-01-20T12:00:00Z',
        estimatedDeliveryTime: '2024-01-20T12:30:00Z'
      },
      {
        id: '4',
        orderNumber: 'ZCH1640995500000',
        customerInfo: {
          name: 'Sarah Wilson',
          phone: '+237666789012',
          email: '<EMAIL>'
        },
        items: [
          {
            menuItem: { name: 'Fried Rice with Chicken', price: 2500 },
            quantity: 1,
            price: 2500
          }
        ],
        totalAmount: 3000,
        status: 'delivered',
        paymentStatus: 'paid',
        paymentMethod: 'cash-on-delivery',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '789 Pine St',
            city: 'Douala'
          }
        },
        createdAt: '2024-01-20T09:00:00Z',
        estimatedDeliveryTime: '2024-01-20T09:30:00Z',
        deliveredAt: '2024-01-20T09:25:00Z'
      }
    ];
    setOrders(mockOrders);
    setFilteredOrders(mockOrders);
  }, []);

  // Filter orders based on status and search term
  useEffect(() => {
    let filtered = orders;

    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerInfo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerInfo.phone.includes(searchTerm)
      );
    }

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchTerm]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
      case 'confirmed':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'preparing':
        return <ClockIcon className="h-5 w-5 text-orange-500" />;
      case 'ready':
      case 'out-for-delivery':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'preparing':
        return 'bg-orange-100 text-orange-800';
      case 'ready':
        return 'bg-purple-100 text-purple-800';
      case 'out-for-delivery':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    setOrders(orders.map(order =>
      order.id === orderId
        ? {
            ...order,
            status: newStatus,
            ...(newStatus === 'delivered' && { deliveredAt: new Date().toISOString() })
          }
        : order
    ));
    toast.success(`Order status updated to ${newStatus}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US').format(amount);
  };

  const statusOptions = [
    { value: 'all', label: 'All Orders' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'preparing', label: 'Preparing' },
    { value: 'ready', label: 'Ready' },
    { value: 'out-for-delivery', label: 'Out for Delivery' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Order Management</h1>
            <p className="text-gray-600 mt-2">Manage and track all customer orders</p>
          </div>
          <div className="mt-4 sm:mt-0 text-sm text-gray-500">
            Total Orders: {filteredOrders.length}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Orders
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by order number, customer name, or phone..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Filter by Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
            >
              <div className="p-6">
                {/* Order Header */}
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                  <div className="flex items-center space-x-3 mb-2 lg:mb-0">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Order #{order.orderNumber}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatDate(order.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('-', ' ')}
                    </span>
                    <Link
                      to={`/owner/orders/${order.id}`}
                      className="flex items-center text-orange-600 hover:text-orange-700 transition-colors"
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      View Details
                    </Link>
                  </div>
                </div>

                {/* Customer Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Customer</p>
                    <p className="font-medium">{order.customerInfo.name}</p>
                    <div className="flex items-center mt-1">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-600">{order.customerInfo.phone}</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Delivery</p>
                    <p className="font-medium">
                      {order.deliveryInfo.type === 'delivery' ? 'Home Delivery' : 'Pickup'}
                    </p>
                    {order.deliveryInfo.type === 'delivery' && order.deliveryInfo.address && (
                      <div className="flex items-start mt-1">
                        <MapPinIcon className="h-4 w-4 text-gray-400 mr-1 mt-0.5" />
                        <span className="text-sm text-gray-600">
                          {order.deliveryInfo.address.street}, {order.deliveryInfo.address.city}
                        </span>
                      </div>
                    )}
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Payment</p>
                    <p className="font-medium">
                      {order.paymentStatus === 'paid' ? 'Paid' : 'Pending'}
                    </p>
                    <p className="text-sm text-gray-600">
                      {order.paymentMethod.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </p>
                  </div>
                </div>

                {/* Order Items */}
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Items ({order.items.length})</p>
                  <div className="space-y-1">
                    {order.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex justify-between text-sm">
                        <span className="text-gray-600">
                          {item.menuItem.name} x{item.quantity}
                          {item.specialInstructions && (
                            <span className="text-gray-500 italic"> - {item.specialInstructions}</span>
                          )}
                        </span>
                        <span className="font-medium">
                          {formatCurrency(item.price * item.quantity)} XAF
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <div className="flex justify-between font-bold">
                      <span>Total</span>
                      <span className="text-orange-600">{formatCurrency(order.totalAmount)} XAF</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  {order.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'confirmed')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        Confirm Order
                      </button>
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'cancelled')}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                      >
                        Cancel Order
                      </button>
                    </>
                  )}

                  {order.status === 'confirmed' && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'preparing')}
                      className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium"
                    >
                      Start Preparing
                    </button>
                  )}

                  {order.status === 'preparing' && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'ready')}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                    >
                      Mark Ready
                    </button>
                  )}

                  {order.status === 'ready' && order.deliveryInfo.type === 'delivery' && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'out-for-delivery')}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-sm font-medium"
                    >
                      Send for Delivery
                    </button>
                  )}

                  {((order.status === 'ready' && order.deliveryInfo.type === 'pickup') ||
                    order.status === 'out-for-delivery') && (
                    <button
                      onClick={() => handleStatusUpdate(order.id, 'delivered')}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                    >
                      Mark Delivered
                    </button>
                  )}

                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                    Call Customer
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <ClockIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No orders found
            </h3>
            <p className="text-gray-600">
              {statusFilter === 'all'
                ? "No orders have been placed yet."
                : `No orders with status "${statusFilter}" found.`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderManagement;
