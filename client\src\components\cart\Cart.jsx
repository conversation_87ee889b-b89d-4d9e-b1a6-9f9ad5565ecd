import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  XMarkIcon,
  MinusIcon,
  PlusIcon,
  ShoppingBagIcon 
} from '@heroicons/react/24/outline';
import { 
  closeCart, 
  updateQuantity, 
  removeFromCart 
} from '../../store/slices/cartSlice';

const Cart = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { items, totalAmount, totalItems, isOpen } = useSelector((state) => state.cart);

  const handleUpdateQuantity = (menuItemId, specialInstructions, newQuantity) => {
    dispatch(updateQuantity({ 
      menuItemId, 
      specialInstructions, 
      quantity: newQuantity 
    }));
  };

  const handleRemoveItem = (menuItemId, specialInstructions) => {
    dispatch(removeFromCart({ menuItemId, specialInstructions }));
  };

  const handleClose = () => {
    dispatch(closeCart());
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
          />

          {/* Cart Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <ShoppingBagIcon className="h-5 w-5 mr-2" />
                {t('cart')} ({totalItems})
              </h2>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-4">
              {items.length === 0 ? (
                <div className="text-center py-12">
                  <ShoppingBagIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Your cart is empty</p>
                  <Link
                    to="/menu"
                    onClick={handleClose}
                    className="mt-4 inline-block bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Browse Menu
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <motion.div
                      key={`${item.menuItem._id}-${item.specialInstructions}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gray-50 rounded-lg p-4"
                    >
                      <div className="flex items-start space-x-3">
                        {/* Item Image */}
                        <img
                          src={item.menuItem.images?.[0]?.url || '/images/placeholder-food.jpg'}
                          alt={item.menuItem.name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />

                        {/* Item Details */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {item.menuItem.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {item.price} XAF each
                          </p>
                          {item.specialInstructions && (
                            <p className="text-xs text-gray-400 mt-1">
                              Note: {item.specialInstructions}
                            </p>
                          )}

                          {/* Quantity Controls */}
                          <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleUpdateQuantity(
                                  item.menuItem._id, 
                                  item.specialInstructions, 
                                  item.quantity - 1
                                )}
                                className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                                disabled={item.quantity <= 1}
                              >
                                <MinusIcon className="h-4 w-4" />
                              </button>
                              <span className="text-sm font-medium w-8 text-center">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleUpdateQuantity(
                                  item.menuItem._id, 
                                  item.specialInstructions, 
                                  item.quantity + 1
                                )}
                                className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                              >
                                <PlusIcon className="h-4 w-4" />
                              </button>
                            </div>

                            {/* Remove Button */}
                            <button
                              onClick={() => handleRemoveItem(
                                item.menuItem._id, 
                                item.specialInstructions
                              )}
                              className="text-red-500 hover:text-red-700 text-sm transition-colors"
                            >
                              Remove
                            </button>
                          </div>

                          {/* Item Total */}
                          <div className="text-right mt-2">
                            <span className="text-sm font-semibold text-gray-900">
                              {item.price * item.quantity} XAF
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <div className="border-t border-gray-200 p-4 space-y-4">
                {/* Total */}
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">
                    {t('orderTotal')}:
                  </span>
                  <span className="text-lg font-bold text-orange-600">
                    {totalAmount} XAF
                  </span>
                </div>

                {/* Checkout Button */}
                <Link
                  to="/checkout"
                  onClick={handleClose}
                  className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors text-center block font-semibold"
                >
                  {t('checkout')}
                </Link>

                {/* Continue Shopping */}
                <Link
                  to="/menu"
                  onClick={handleClose}
                  className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-center block"
                >
                  Continue Shopping
                </Link>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default Cart;
