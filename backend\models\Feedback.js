import mongoose from 'mongoose';

const feedbackSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  ratings: {
    food: {
      type: Number,
      required: [true, 'Food rating is required'],
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5']
    },
    service: {
      type: Number,
      required: [true, 'Service rating is required'],
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5']
    },
    delivery: {
      type: Number,
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5'],
      required: function() {
        return this.order && this.order.deliveryInfo && this.order.deliveryInfo.type === 'delivery';
      }
    },
    overall: {
      type: Number,
      required: [true, 'Overall rating is required'],
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5']
    }
  },
  review: {
    type: String,
    trim: true,
    maxlength: [1000, 'Review cannot exceed 1000 characters']
  },
  itemFeedback: [{
    menuItem: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MenuItem',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5']
    },
    comment: {
      type: String,
      trim: true,
      maxlength: [500, 'Comment cannot exceed 500 characters']
    }
  }],
  improvements: [{
    category: {
      type: String,
      enum: ['food-quality', 'portion-size', 'delivery-time', 'packaging', 'customer-service', 'pricing', 'menu-variety', 'other']
    },
    suggestion: {
      type: String,
      trim: true,
      maxlength: [500, 'Suggestion cannot exceed 500 characters']
    }
  }],
  wouldRecommend: {
    type: Boolean,
    required: true
  },
  wouldOrderAgain: {
    type: Boolean,
    required: true
  },
  favoriteItems: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MenuItem'
  }],
  images: [{
    url: String,
    publicId: String, // Cloudinary public ID
    caption: String
  }],
  isAnonymous: {
    type: Boolean,
    default: false
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'pending'
  },
  moderationNotes: String,
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: Date,
  helpfulVotes: {
    type: Number,
    default: 0
  },
  reportCount: {
    type: Number,
    default: 0
  },
  response: {
    message: String,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    respondedAt: Date
  }
}, {
  timestamps: true
});

// Indexes for better query performance
feedbackSchema.index({ order: 1 });
feedbackSchema.index({ customer: 1 });
feedbackSchema.index({ 'ratings.overall': -1 });
feedbackSchema.index({ status: 1 });
feedbackSchema.index({ isPublic: 1 });
feedbackSchema.index({ createdAt: -1 });

// Virtual for average rating calculation
feedbackSchema.virtual('averageRating').get(function() {
  const ratings = [this.ratings.food, this.ratings.service];
  if (this.ratings.delivery) ratings.push(this.ratings.delivery);
  
  return ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
});

// Method to approve feedback
feedbackSchema.methods.approve = function(moderatorId, notes = '') {
  this.status = 'approved';
  this.moderatedBy = moderatorId;
  this.moderatedAt = new Date();
  this.moderationNotes = notes;
  return this.save();
};

// Method to reject feedback
feedbackSchema.methods.reject = function(moderatorId, notes = '') {
  this.status = 'rejected';
  this.moderatedBy = moderatorId;
  this.moderatedAt = new Date();
  this.moderationNotes = notes;
  return this.save();
};

// Method to add owner response
feedbackSchema.methods.addResponse = function(message, responderId) {
  this.response = {
    message,
    respondedBy: responderId,
    respondedAt: new Date()
  };
  return this.save();
};

// Static method to get average ratings for a menu item
feedbackSchema.statics.getMenuItemAverageRating = async function(menuItemId) {
  const result = await this.aggregate([
    {
      $match: {
        'itemFeedback.menuItem': menuItemId,
        status: 'approved',
        isPublic: true
      }
    },
    {
      $unwind: '$itemFeedback'
    },
    {
      $match: {
        'itemFeedback.menuItem': menuItemId
      }
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$itemFeedback.rating' },
        totalReviews: { $sum: 1 }
      }
    }
  ]);
  
  return result.length > 0 ? result[0] : { averageRating: 0, totalReviews: 0 };
};

export default mongoose.model('Feedback', feedbackSchema);
