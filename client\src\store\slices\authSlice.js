import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;
      localStorage.setItem('token', action.payload.token);
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.error = action.payload;
      localStorage.removeItem('token');
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.error = null;
      localStorage.removeItem('token');
    },
    demoLogin: (state, action) => {
      const { role } = action.payload;
      const demoUsers = {
        customer: {
          id: '1',
          name: '<PERSON>',
          email: '<EMAIL>',
          role: 'customer',
          phone: '+237123456789'
        },
        owner: {
          id: '2',
          name: 'Sarah Owner',
          email: '<EMAIL>',
          role: 'owner',
          phone: '+237987654321'
        },
        delivery: {
          id: '3',
          name: '<PERSON>ivery',
          email: '<EMAIL>',
          role: 'delivery',
          phone: '+237555123456'
        }
      };

      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = demoUsers[role];
      state.token = `demo-token-${role}`;
      state.error = null;
      localStorage.setItem('token', `demo-token-${role}`);
    },
    updateProfile: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateProfile,
  clearError,
  setLoading,
  demoLogin,
} = authSlice.actions;

export default authSlice.reducer;
