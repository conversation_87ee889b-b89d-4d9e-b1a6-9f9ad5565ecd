import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  MinusIcon,
  PlusIcon,
  TrashIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';
import {
  updateQuantity,
  removeFromCart
} from '../store/slices/cartSlice';

const Cart = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { items, totalAmount, totalItems } = useSelector((state) => state.cart);

  const handleUpdateQuantity = (menuItemId, specialInstructions, newQuantity) => {
    dispatch(updateQuantity({
      menuItemId,
      specialInstructions,
      quantity: newQuantity
    }));
  };

  const handleRemoveItem = (menuItemId, specialInstructions) => {
    dispatch(removeFromCart({ menuItemId, specialInstructions }));
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <ShoppingBagIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Your cart is empty
            </h1>
            <p className="text-gray-600 mb-8">
              Add some delicious items from our menu to get started!
            </p>
            <Link
              to="/menu"
              className="bg-orange-600 text-white px-8 py-3 rounded-lg hover:bg-orange-700 transition-colors font-semibold"
            >
              Browse Menu
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          {t('cart')} ({totalItems} items)
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {items.map((item, index) => (
              <motion.div
                key={`${item.menuItem._id}-${item.specialInstructions}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-start space-x-4">
                  {/* Item Image */}
                  <img
                    src={item.menuItem.images?.[0]?.url || '/images/placeholder-food.jpg'}
                    alt={item.menuItem.name}
                    className="w-20 h-20 object-cover rounded-lg"
                  />

                  {/* Item Details */}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {item.menuItem.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">
                      {item.menuItem.description}
                    </p>
                    <p className="text-orange-600 font-semibold">
                      {item.price} XAF each
                    </p>
                    {item.specialInstructions && (
                      <p className="text-sm text-gray-500 mt-1">
                        Note: {item.specialInstructions}
                      </p>
                    )}
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex flex-col items-end space-y-2">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleUpdateQuantity(
                          item.menuItem._id,
                          item.specialInstructions,
                          item.quantity - 1
                        )}
                        className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                        disabled={item.quantity <= 1}
                      >
                        <MinusIcon className="h-4 w-4" />
                      </button>
                      <span className="text-lg font-medium w-8 text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => handleUpdateQuantity(
                          item.menuItem._id,
                          item.specialInstructions,
                          item.quantity + 1
                        )}
                        className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      >
                        <PlusIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Item Total */}
                    <p className="text-lg font-bold text-gray-900">
                      {item.price * item.quantity} XAF
                    </p>

                    {/* Remove Button */}
                    <button
                      onClick={() => handleRemoveItem(
                        item.menuItem._id,
                        item.specialInstructions
                      )}
                      className="text-red-500 hover:text-red-700 p-1 transition-colors"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('orderSummary')}
              </h2>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-semibold">{totalAmount} XAF</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee</span>
                  <span className="font-semibold">
                    {totalAmount >= 5000 ? 'Free' : '500 XAF'}
                  </span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-orange-600">
                      {totalAmount >= 5000 ? totalAmount : totalAmount + 500} XAF
                    </span>
                  </div>
                </div>
              </div>

              {totalAmount < 5000 && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                  <p className="text-sm text-orange-700">
                    Add {5000 - totalAmount} XAF more for free delivery!
                  </p>
                </div>
              )}

              <Link
                to="/checkout"
                className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors text-center block font-semibold"
              >
                {t('checkout')}
              </Link>

              <Link
                to="/menu"
                className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-center block mt-3"
              >
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
