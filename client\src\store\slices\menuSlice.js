import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  items: [],
  currentItem: null,
  categories: ['main-course', 'side-dish', 'special', 'beverage', 'dessert'],
  filters: {
    category: '',
    day: '',
    available: true,
    search: '',
  },
  weeklyMenu: {},
  isLoading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
};

const menuSlice = createSlice({
  name: 'menu',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setMenuItems: (state, action) => {
      state.items = action.payload.menuItems;
      state.pagination = action.payload.pagination;
      state.isLoading = false;
      state.error = null;
    },
    setCurrentItem: (state, action) => {
      state.currentItem = action.payload;
    },
    clearCurrentItem: (state) => {
      state.currentItem = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        category: '',
        day: '',
        available: true,
        search: '',
      };
    },
    setWeeklyMenu: (state, action) => {
      const { day, menuItems } = action.payload;
      state.weeklyMenu[day] = menuItems;
    },
    updateMenuItem: (state, action) => {
      const updatedItem = action.payload;
      const index = state.items.findIndex(item => item._id === updatedItem._id);
      if (index !== -1) {
        state.items[index] = updatedItem;
      }
      if (state.currentItem && state.currentItem._id === updatedItem._id) {
        state.currentItem = updatedItem;
      }
    },
    addMenuItem: (state, action) => {
      state.items.unshift(action.payload);
    },
    removeMenuItem: (state, action) => {
      const itemId = action.payload;
      state.items = state.items.filter(item => item._id !== itemId);
      if (state.currentItem && state.currentItem._id === itemId) {
        state.currentItem = null;
      }
    },
  },
});

export const {
  setLoading,
  setError,
  clearError,
  setMenuItems,
  setCurrentItem,
  clearCurrentItem,
  setFilters,
  clearFilters,
  setWeeklyMenu,
  updateMenuItem,
  addMenuItem,
  removeMenuItem,
} = menuSlice.actions;

export default menuSlice.reducer;
