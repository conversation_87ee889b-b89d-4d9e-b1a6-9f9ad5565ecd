"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "I18nContext", {
  enumerable: true,
  get: function () {
    return _context.I18nContext;
  }
});
Object.defineProperty(exports, "I18nextProvider", {
  enumerable: true,
  get: function () {
    return _I18nextProvider.I18nextProvider;
  }
});
Object.defineProperty(exports, "Trans", {
  enumerable: true,
  get: function () {
    return _Trans.Trans;
  }
});
Object.defineProperty(exports, "TransWithoutContext", {
  enumerable: true,
  get: function () {
    return _TransWithoutContext.Trans;
  }
});
Object.defineProperty(exports, "Translation", {
  enumerable: true,
  get: function () {
    return _Translation.Translation;
  }
});
Object.defineProperty(exports, "composeInitialProps", {
  enumerable: true,
  get: function () {
    return _context.composeInitialProps;
  }
});
exports.date = void 0;
Object.defineProperty(exports, "getDefaults", {
  enumerable: true,
  get: function () {
    return _defaults.getDefaults;
  }
});
Object.defineProperty(exports, "getI18n", {
  enumerable: true,
  get: function () {
    return _i18nInstance.getI18n;
  }
});
Object.defineProperty(exports, "getInitialProps", {
  enumerable: true,
  get: function () {
    return _context.getInitialProps;
  }
});
Object.defineProperty(exports, "initReactI18next", {
  enumerable: true,
  get: function () {
    return _initReactI18next.initReactI18next;
  }
});
exports.selectOrdinal = exports.select = exports.plural = exports.number = void 0;
Object.defineProperty(exports, "setDefaults", {
  enumerable: true,
  get: function () {
    return _defaults.setDefaults;
  }
});
Object.defineProperty(exports, "setI18n", {
  enumerable: true,
  get: function () {
    return _i18nInstance.setI18n;
  }
});
exports.time = void 0;
Object.defineProperty(exports, "useSSR", {
  enumerable: true,
  get: function () {
    return _useSSR.useSSR;
  }
});
Object.defineProperty(exports, "useTranslation", {
  enumerable: true,
  get: function () {
    return _useTranslation.useTranslation;
  }
});
Object.defineProperty(exports, "withSSR", {
  enumerable: true,
  get: function () {
    return _withSSR.withSSR;
  }
});
Object.defineProperty(exports, "withTranslation", {
  enumerable: true,
  get: function () {
    return _withTranslation.withTranslation;
  }
});
var _Trans = require("./Trans.js");
var _TransWithoutContext = require("./TransWithoutContext.js");
var _useTranslation = require("./useTranslation.js");
var _withTranslation = require("./withTranslation.js");
var _Translation = require("./Translation.js");
var _I18nextProvider = require("./I18nextProvider.js");
var _withSSR = require("./withSSR.js");
var _useSSR = require("./useSSR.js");
var _initReactI18next = require("./initReactI18next.js");
var _defaults = require("./defaults.js");
var _i18nInstance = require("./i18nInstance.js");
var _context = require("./context.js");
const date = () => '';
exports.date = date;
const time = () => '';
exports.time = time;
const number = () => '';
exports.number = number;
const select = () => '';
exports.select = select;
const plural = () => '';
exports.plural = plural;
const selectOrdinal = () => '';
exports.selectOrdinal = selectOrdinal;