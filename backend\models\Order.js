import mongoose from 'mongoose';

const orderItemSchema = new mongoose.Schema({
  menuItem: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MenuItem',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  specialInstructions: String
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [orderItemSchema],
  totalAmount: {
    type: Number,
    required: true,
    min: [0, 'Total amount cannot be negative']
  },
  status: {
    type: String,
    enum: [
      'pending',
      'confirmed',
      'preparing',
      'ready',
      'out-for-delivery',
      'delivered',
      'cancelled',
      'refunded'
    ],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash-on-delivery', 'mtn-mobile-money', 'orange-money', 'card'],
    default: 'cash-on-delivery'
  },
  deliveryInfo: {
    type: {
      type: String,
      enum: ['delivery', 'pickup'],
      required: true
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: {
        type: String,
        default: 'Cameroon'
      },
      coordinates: {
        latitude: Number,
        longitude: Number
      },
      instructions: String
    },
    scheduledTime: Date,
    estimatedDeliveryTime: Date,
    actualDeliveryTime: Date
  },
  assignedDeliveryPartner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  customerNotes: String,
  internalNotes: String,
  statusHistory: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  estimatedPreparationTime: {
    type: Number, // in minutes
    default: 30
  },
  actualPreparationTime: Number,
  rating: {
    food: {
      type: Number,
      min: 1,
      max: 5
    },
    delivery: {
      type: Number,
      min: 1,
      max: 5
    },
    overall: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  feedback: String,
  isSpecialOrder: {
    type: Boolean,
    default: false
  },
  refundAmount: {
    type: Number,
    default: 0
  },
  refundReason: String
}, {
  timestamps: true
});

// Indexes for better query performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customer: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ assignedDeliveryPartner: 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'deliveryInfo.scheduledTime': 1 });

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.orderNumber = `ZCH${dateStr}${randomNum}`;
  }
  next();
});

// Method to add status update
orderSchema.methods.updateStatus = function(newStatus, updatedBy, notes = '') {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    updatedBy,
    notes
  });
  
  // Update timestamps based on status
  if (newStatus === 'delivered') {
    this.deliveryInfo.actualDeliveryTime = new Date();
  }
  
  return this.save();
};

// Method to calculate estimated delivery time
orderSchema.methods.calculateEstimatedDeliveryTime = function() {
  const now = new Date();
  const prepTime = this.estimatedPreparationTime || 30;
  const deliveryTime = this.deliveryInfo.type === 'delivery' ? 20 : 0; // 20 min for delivery
  
  this.deliveryInfo.estimatedDeliveryTime = new Date(now.getTime() + (prepTime + deliveryTime) * 60000);
  return this.save();
};

// Virtual for order age in minutes
orderSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60));
});

export default mongoose.model('Order', orderSchema);
