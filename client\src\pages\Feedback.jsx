import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import {
  StarIcon,
  CheckCircleIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const Feedback = () => {
  const { orderId } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [overallRating, setOverallRating] = useState(0);
  const [deliveryRating, setDeliveryRating] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm();

  // Mock order data
  useEffect(() => {
    // In real app, fetch order details from API
    const mockOrder = {
      id: orderId,
      orderNumber: `ZCH${orderId}`,
      customerName: 'John Doe',
      items: [
        { name: 'Fried Rice with Chicken', quantity: 2, price: 2500 },
        { name: 'Fried Plantains', quantity: 1, price: 1500 }
      ],
      totalAmount: 6500,
      status: 'delivered',
      deliveredAt: '2024-01-20T14:30:00Z',
      deliveryPartner: 'Mike Johnson'
    };
    setOrder(mockOrder);
  }, [orderId]);

  const onSubmit = async (data) => {
    if (overallRating === 0) {
      toast.error('Please provide an overall rating');
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const feedbackData = {
        orderId,
        overallRating,
        deliveryRating,
        foodQuality: data.foodQuality,
        comment: data.comment,
        wouldRecommend: data.wouldRecommend === 'yes',
        submittedAt: new Date().toISOString()
      };

      console.log('Feedback submitted:', feedbackData);

      setIsSubmitted(true);
      toast.success('Thank you for your feedback!');
    } catch (error) {
      toast.error('Failed to submit feedback. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const StarRating = ({ rating, setRating, label }) => {
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label} *
        </label>
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => setRating(star)}
              className="focus:outline-none"
            >
              {star <= rating ? (
                <StarIconSolid className="h-8 w-8 text-yellow-400" />
              ) : (
                <StarIcon className="h-8 w-8 text-gray-300 hover:text-yellow-400 transition-colors" />
              )}
            </button>
          ))}
        </div>
        <p className="text-sm text-gray-500 mt-1">
          {rating === 0 && 'Click to rate'}
          {rating === 1 && 'Poor'}
          {rating === 2 && 'Fair'}
          {rating === 3 && 'Good'}
          {rating === 4 && 'Very Good'}
          {rating === 5 && 'Excellent'}
        </p>
      </div>
    );
  };

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading order details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Thank You for Your Feedback!
            </h1>
            <p className="text-gray-600 mb-8">
              Your feedback helps us improve our service and food quality.
            </p>
            <div className="space-x-4">
              <button
                onClick={() => navigate('/orders')}
                className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
              >
                View My Orders
              </button>
              <button
                onClick={() => navigate('/menu')}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Order Again
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate('/orders')}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors mr-4"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-1" />
            Back to Orders
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Rate Your Experience
            </h1>
            <p className="text-gray-600 mt-2">
              Order #{order.orderNumber}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Order Summary
              </h2>

              <div className="space-y-3 mb-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-gray-600">{item.name} x{item.quantity}</span>
                    <span className="font-medium">{item.price * item.quantity} XAF</span>
                  </div>
                ))}
              </div>

              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span className="text-orange-600">{order.totalAmount} XAF</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-500">Delivered by</p>
                <p className="font-medium">{order.deliveryPartner}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {new Date(order.deliveredAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Feedback Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Share Your Experience
              </h2>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Overall Rating */}
                <StarRating
                  rating={overallRating}
                  setRating={setOverallRating}
                  label="Overall Experience"
                />

                {/* Delivery Rating */}
                <StarRating
                  rating={deliveryRating}
                  setRating={setDeliveryRating}
                  label="Delivery Service"
                />

                {/* Food Quality */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    How was the food quality?
                  </label>
                  <div className="space-y-2">
                    {['excellent', 'good', 'average', 'poor'].map((quality) => (
                      <label key={quality} className="flex items-center">
                        <input
                          {...register('foodQuality', { required: 'Please select food quality' })}
                          type="radio"
                          value={quality}
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700 capitalize">{quality}</span>
                      </label>
                    ))}
                  </div>
                  {errors.foodQuality && (
                    <p className="text-red-500 text-sm mt-1">{errors.foodQuality.message}</p>
                  )}
                </div>

                {/* Would Recommend */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Would you recommend us to others?
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        {...register('wouldRecommend', { required: 'Please select an option' })}
                        type="radio"
                        value="yes"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">Yes, definitely!</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        {...register('wouldRecommend')}
                        type="radio"
                        value="maybe"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">Maybe</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        {...register('wouldRecommend')}
                        type="radio"
                        value="no"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">No</span>
                    </label>
                  </div>
                  {errors.wouldRecommend && (
                    <p className="text-red-500 text-sm mt-1">{errors.wouldRecommend.message}</p>
                  )}
                </div>

                {/* Comments */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Comments (Optional)
                  </label>
                  <textarea
                    {...register('comment')}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Tell us more about your experience..."
                  />
                </div>

                {/* Submit Button */}
                <div className="flex space-x-4">
                  <motion.button
                    type="submit"
                    disabled={isLoading}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex-1 bg-orange-600 text-white py-3 px-6 rounded-lg hover:bg-orange-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Submitting...
                      </div>
                    ) : (
                      'Submit Feedback'
                    )}
                  </motion.button>
                  <button
                    type="button"
                    onClick={() => navigate('/orders')}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    Skip for Now
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Feedback;
