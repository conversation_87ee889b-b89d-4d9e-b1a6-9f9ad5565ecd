import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const Orders = () => {
  const { t } = useTranslation();
  const { orders } = useSelector((state) => state.orders);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock orders data - in real app this would come from API
  const mockOrders = [
    {
      id: '1',
      orderNumber: 'ZCH1640995200000',
      status: 'delivered',
      paymentStatus: 'paid',
      totalAmount: 7500,
      createdAt: '2024-01-15T10:30:00Z',
      items: [
        { menuItem: { name: 'Fried Rice with Chicken' }, quantity: 2, price: 2500 },
        { menuItem: { name: 'Fried Plantains' }, quantity: 1, price: 1500 }
      ],
      deliveryInfo: {
        type: 'delivery',
        address: { street: '123 Main St', city: 'Douala' }
      }
    },
    {
      id: '2',
      orderNumber: 'ZCH1640995300000',
      status: 'preparing',
      paymentStatus: 'pending',
      totalAmount: 6000,
      createdAt: '2024-01-16T14:15:00Z',
      items: [
        { menuItem: { name: 'Water Fufu and Eru' }, quantity: 2, price: 3000 }
      ],
      deliveryInfo: {
        type: 'pickup'
      }
    }
  ];

  useEffect(() => {
    // In real app, fetch orders from API
    const ordersToShow = orders.length > 0 ? orders : mockOrders;

    if (statusFilter === 'all') {
      setFilteredOrders(ordersToShow);
    } else {
      setFilteredOrders(ordersToShow.filter(order => order.status === statusFilter));
    }
  }, [orders, statusFilter]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
      case 'confirmed':
      case 'preparing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'ready':
      case 'out-for-delivery':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'preparing':
        return 'bg-orange-100 text-orange-800';
      case 'ready':
        return 'bg-purple-100 text-purple-800';
      case 'out-for-delivery':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {t('orderHistory')}
          </h1>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All Orders</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="preparing">Preparing</option>
            <option value="ready">Ready</option>
            <option value="out-for-delivery">Out for Delivery</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <ClockIcon className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              No orders found
            </h2>
            <p className="text-gray-600 mb-8">
              {statusFilter === 'all'
                ? "You haven't placed any orders yet."
                : `No orders with status "${statusFilter}" found.`}
            </p>
            <Link
              to="/menu"
              className="bg-orange-600 text-white px-8 py-3 rounded-lg hover:bg-orange-700 transition-colors font-semibold"
            >
              Browse Menu
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredOrders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
              >
                <div className="p-6">
                  {/* Order Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                    <div className="flex items-center space-x-3 mb-2 sm:mb-0">
                      {getStatusIcon(order.status)}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Order #{order.orderNumber}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1).replace('-', ' ')}
                      </span>
                      <Link
                        to={`/orders/${order.id}`}
                        className="flex items-center text-orange-600 hover:text-orange-700 transition-colors"
                      >
                        <EyeIcon className="h-4 w-4 mr-1" />
                        View Details
                      </Link>
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Items:</h4>
                    <div className="space-y-1">
                      {order.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex justify-between text-sm">
                          <span className="text-gray-600">
                            {item.menuItem.name} x{item.quantity}
                          </span>
                          <span className="font-medium">
                            {item.price * item.quantity} XAF
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Order Details */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Delivery:</span>
                      <p className="font-medium">
                        {order.deliveryInfo.type === 'delivery' ? 'Home Delivery' : 'Pickup'}
                      </p>
                      {order.deliveryInfo.type === 'delivery' && order.deliveryInfo.address && (
                        <p className="text-gray-600 text-xs">
                          {order.deliveryInfo.address.street}, {order.deliveryInfo.address.city}
                        </p>
                      )}
                    </div>
                    <div>
                      <span className="text-gray-500">Payment:</span>
                      <p className="font-medium">
                        {order.paymentStatus === 'paid' ? 'Paid' : 'Pending'}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">Total:</span>
                      <p className="font-bold text-orange-600">
                        {order.totalAmount} XAF
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 flex flex-col sm:flex-row gap-2">
                    {order.status === 'delivered' && (
                      <Link
                        to={`/feedback/${order.id}`}
                        className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-center text-sm font-medium"
                      >
                        Leave Feedback
                      </Link>
                    )}
                    {(order.status === 'pending' || order.status === 'confirmed') && (
                      <button className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors text-sm font-medium">
                        Cancel Order
                      </button>
                    )}
                    <Link
                      to={`/orders/${order.id}`}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-center text-sm font-medium"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;
