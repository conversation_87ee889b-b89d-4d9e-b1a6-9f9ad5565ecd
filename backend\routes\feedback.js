import express from 'express';
import { body, validationResult } from 'express-validator';
import Feedback from '../models/Feedback.js';
import Order from '../models/Order.js';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Validation rules
const feedbackValidation = [
  body('order').isMongoId().withMessage('Valid order ID is required'),
  body('ratings.food').isInt({ min: 1, max: 5 }).withMessage('Food rating must be between 1 and 5'),
  body('ratings.service').isInt({ min: 1, max: 5 }).withMessage('Service rating must be between 1 and 5'),
  body('ratings.overall').isInt({ min: 1, max: 5 }).withMessage('Overall rating must be between 1 and 5'),
  body('wouldRecommend').isBoolean().withMessage('Would recommend must be true or false'),
  body('wouldOrderAgain').isBoolean().withMessage('Would order again must be true or false')
];

// @route   POST /api/feedback
// @desc    Submit feedback for an order
// @access  Private (Customer)
router.post('/', authenticate, feedbackValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { order: orderId } = req.body;

    // Verify order belongs to customer and is delivered
    const order = await Order.findOne({
      _id: orderId,
      customer: req.user.id,
      status: 'delivered'
    });

    if (!order) {
      return res.status(400).json({
        success: false,
        message: 'Order not found or not eligible for feedback'
      });
    }

    // Check if feedback already exists
    const existingFeedback = await Feedback.findOne({ order: orderId });
    if (existingFeedback) {
      return res.status(400).json({
        success: false,
        message: 'Feedback already submitted for this order'
      });
    }

    const feedback = new Feedback({
      ...req.body,
      customer: req.user.id
    });

    await feedback.save();

    // Update order with rating
    order.rating = {
      food: feedback.ratings.food,
      delivery: feedback.ratings.delivery,
      overall: feedback.ratings.overall
    };
    order.feedback = feedback.review;
    await order.save();

    await feedback.populate([
      { path: 'order', select: 'orderNumber' },
      { path: 'customer', select: 'name' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Feedback submitted successfully',
      data: { feedback }
    });
  } catch (error) {
    console.error('Submit feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit feedback',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/feedback
// @desc    Get feedback (filtered by role)
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const { status = 'approved', page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    let query = {};

    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'owner') {
      // Owners can see all feedback
      if (status) query.status = status;
    } else {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const feedback = await Feedback.find(query)
      .populate([
        { path: 'order', select: 'orderNumber totalAmount' },
        { path: 'customer', select: 'name' },
        { path: 'itemFeedback.menuItem', select: 'name' }
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Feedback.countDocuments(query);

    res.json({
      success: true,
      data: {
        feedback,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get feedback',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/feedback/public
// @desc    Get public feedback for display
// @access  Public
router.get('/public', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const feedback = await Feedback.find({
      status: 'approved',
      isPublic: true,
      isAnonymous: false
    })
      .populate([
        { path: 'customer', select: 'name' },
        { path: 'itemFeedback.menuItem', select: 'name' }
      ])
      .select('-customer.email -customer.phone')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Feedback.countDocuments({
      status: 'approved',
      isPublic: true
    });

    res.json({
      success: true,
      data: {
        feedback,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get public feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get public feedback',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/feedback/:id/moderate
// @desc    Moderate feedback (approve/reject)
// @access  Private (Owner only)
router.patch('/:id/moderate', authenticate, authorize('owner'), [
  body('action').isIn(['approve', 'reject']).withMessage('Action must be approve or reject'),
  body('notes').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { action, notes = '' } = req.body;
    const feedback = await Feedback.findById(req.params.id);

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: 'Feedback not found'
      });
    }

    if (action === 'approve') {
      await feedback.approve(req.user.id, notes);
    } else {
      await feedback.reject(req.user.id, notes);
    }

    res.json({
      success: true,
      message: `Feedback ${action}d successfully`,
      data: { feedback }
    });
  } catch (error) {
    console.error('Moderate feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to moderate feedback',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
