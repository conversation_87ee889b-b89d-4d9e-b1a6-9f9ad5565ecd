import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';

const Footer = () => {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-orange-400 mb-4">
              <PERSON><PERSON>
            </h3>
            <p className="text-gray-300 mb-4">
              {t('menuDescription')}
            </p>
            <div className="space-y-2">
              <div className="flex items-center">
                <MapPinIcon className="h-5 w-5 text-orange-400 mr-2" />
                <span className="text-gray-300">Douala, Cameroon</span>
              </div>
              <div className="flex items-center">
                <PhoneIcon className="h-5 w-5 text-orange-400 mr-2" />
                <span className="text-gray-300">+*********** 789</span>
              </div>
              <div className="flex items-center">
                <EnvelopeIcon className="h-5 w-5 text-orange-400 mr-2" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-orange-400 transition-colors">
                  {t('home')}
                </Link>
              </li>
              <li>
                <Link to="/menu" className="text-gray-300 hover:text-orange-400 transition-colors">
                  {t('menu')}
                </Link>
              </li>
              <li>
                <Link to="/special-orders" className="text-gray-300 hover:text-orange-400 transition-colors">
                  {t('specialOrders')}
                </Link>
              </li>
              <li>
                <a href="#about" className="text-gray-300 hover:text-orange-400 transition-colors">
                  {t('aboutUs')}
                </a>
              </li>
              <li>
                <a href="#contact" className="text-gray-300 hover:text-orange-400 transition-colors">
                  {t('contactUs')}
                </a>
              </li>
            </ul>
          </div>

          {/* Opening Hours */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <ClockIcon className="h-5 w-5 text-orange-400 mr-2" />
              Opening Hours
            </h4>
            <div className="space-y-2 text-gray-300">
              <div className="flex justify-between">
                <span>Mon - Fri:</span>
                <span>10:00 - 22:00</span>
              </div>
              <div className="flex justify-between">
                <span>Saturday:</span>
                <span>10:00 - 23:00</span>
              </div>
              <div className="flex justify-between">
                <span>Sunday:</span>
                <span>12:00 - 21:00</span>
              </div>
            </div>
          </div>
        </div>

        {/* Weekly Menu Schedule */}
        <div className="mt-8 pt-8 border-t border-gray-700">
          <h4 className="text-lg font-semibold mb-4 text-center">
            {t('weeklyMenu')}
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-7 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('monday')}</div>
              <div className="text-gray-300">Fried Rice & Chicken</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('tuesday')}</div>
              <div className="text-gray-300">Water Fufu & Eru</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('wednesday')}</div>
              <div className="text-gray-300">Fried Rice & Chicken</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('thursday')}</div>
              <div className="text-gray-300">Water Fufu & Eru</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('friday')}</div>
              <div className="text-gray-300">Fried Rice / Njama Njama</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('saturday')}</div>
              <div className="text-gray-300">Congo Meat & Plantains</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-400">{t('sunday')}</div>
              <div className="text-gray-300">Closed</div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t border-gray-700 flex flex-col sm:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm">
            © 2025 Zina Chop House. All rights reserved.
          </div>
          <div className="flex space-x-6 mt-4 sm:mt-0">
            <Link to="/privacy" className="text-gray-400 hover:text-orange-400 text-sm transition-colors">
              {t('privacyPolicy')}
            </Link>
            <Link to="/terms" className="text-gray-400 hover:text-orange-400 text-sm transition-colors">
              {t('termsOfService')}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
