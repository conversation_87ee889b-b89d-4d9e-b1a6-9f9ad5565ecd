import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { 
  CalendarDaysIcon,
  UserGroupIcon,
  ClockIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon 
} from '@heroicons/react/24/outline';

const SpecialOrders = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('event');
  const { register, handleSubmit, formState: { errors }, reset } = useForm();

  const onSubmit = async (data) => {
    try {
      // In real app, this would be an API call
      console.log('Special order submitted:', { ...data, type: activeTab });
      
      toast.success(t('specialOrderSubmitted'));
      reset();
    } catch (error) {
      toast.error('Failed to submit special order');
    }
  };

  const eventFields = (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('eventName')} *
          </label>
          <input
            type="text"
            {...register('eventName', { required: 'Event name is required' })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Birthday Party, Wedding, etc."
          />
          {errors.eventName && (
            <p className="text-red-500 text-sm mt-1">{errors.eventName.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('numberOfPeople')} *
          </label>
          <input
            type="number"
            min="1"
            {...register('numberOfPeople', { 
              required: 'Number of people is required',
              min: { value: 1, message: 'Must be at least 1 person' }
            })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="50"
          />
          {errors.numberOfPeople && (
            <p className="text-red-500 text-sm mt-1">{errors.numberOfPeople.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('eventDate')} *
          </label>
          <input
            type="date"
            {...register('eventDate', { required: 'Event date is required' })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          {errors.eventDate && (
            <p className="text-red-500 text-sm mt-1">{errors.eventDate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('eventTime')} *
          </label>
          <input
            type="time"
            {...register('eventTime', { required: 'Event time is required' })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          {errors.eventTime && (
            <p className="text-red-500 text-sm mt-1">{errors.eventTime.message}</p>
          )}
        </div>
      </div>
    </>
  );

  const bulkFields = (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('mealType')} *
          </label>
          <select
            {...register('mealType', { required: 'Meal type is required' })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="">Select meal type</option>
            <option value="fried-rice">Fried Rice with Chicken</option>
            <option value="fufu-eru">Water Fufu and Eru</option>
            <option value="njama-njama">Fufu and Njama Njama</option>
            <option value="congo-meat">Congo Meat with Plantains</option>
            <option value="mixed">Mixed Meals</option>
          </select>
          {errors.mealType && (
            <p className="text-red-500 text-sm mt-1">{errors.mealType.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('servings')} *
          </label>
          <input
            type="number"
            min="10"
            {...register('servings', { 
              required: 'Number of servings is required',
              min: { value: 10, message: 'Minimum 10 servings for bulk orders' }
            })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="50"
          />
          {errors.servings && (
            <p className="text-red-500 text-sm mt-1">{errors.servings.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('deliveryDay')} *
          </label>
          <select
            {...register('deliveryDay', { required: 'Delivery day is required' })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="">Select delivery day</option>
            <option value="monday">{t('monday')}</option>
            <option value="tuesday">{t('tuesday')}</option>
            <option value="wednesday">{t('wednesday')}</option>
            <option value="thursday">{t('thursday')}</option>
            <option value="friday">{t('friday')}</option>
            <option value="saturday">{t('saturday')}</option>
            <option value="sunday">{t('sunday')}</option>
          </select>
          {errors.deliveryDay && (
            <p className="text-red-500 text-sm mt-1">{errors.deliveryDay.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Frequency
          </label>
          <select
            {...register('frequency')}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="one-time">One-time order</option>
            <option value="weekly">Weekly</option>
            <option value="bi-weekly">Bi-weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
      </div>
    </>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('specialOrdersTitle')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Planning an event or need bulk orders? We've got you covered with our special ordering service.
          </p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('event')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'event'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <CalendarDaysIcon className="h-5 w-5 inline mr-2" />
                {t('eventOrders')}
              </button>
              <button
                onClick={() => setActiveTab('bulk')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'bulk'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <UserGroupIcon className="h-5 w-5 inline mr-2" />
                {t('bulkOrders')}
              </button>
            </nav>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Tab Content */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'event' ? eventFields : bulkFields}
            </motion.div>

            {/* Customer Information */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('contactInfo')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('name')} *
                  </label>
                  <input
                    type="text"
                    {...register('customerName', { required: 'Name is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Your full name"
                  />
                  {errors.customerName && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('phoneNumber')} *
                  </label>
                  <input
                    type="tel"
                    {...register('customerPhone', { required: 'Phone number is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="+237 123 456 789"
                  />
                  {errors.customerPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerPhone.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('emailAddress')}
                  </label>
                  <input
                    type="email"
                    {...register('customerEmail')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('deliveryAddress')} *
                  </label>
                  <input
                    type="text"
                    {...register('deliveryAddress', { required: 'Delivery address is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Full delivery address"
                  />
                  {errors.deliveryAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.deliveryAddress.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Special Requests */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('specialRequests')}
              </label>
              <textarea
                {...register('specialRequests')}
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Any special dietary requirements, preferences, or additional information..."
              />
            </div>

            {/* Call to Confirm */}
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('callToConfirm')}
                className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                {t('callToConfirm')}
              </label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-orange-600 text-white px-8 py-3 rounded-lg hover:bg-orange-700 transition-colors font-semibold"
              >
                Submit Special Order
              </button>
            </div>
          </form>
        </div>

        {/* Info Section */}
        <div className="bg-orange-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            How it works
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-orange-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-orange-600 font-bold">1</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Submit Order</h4>
              <p className="text-sm text-gray-600">
                Fill out the form with your event or bulk order details
              </p>
            </div>
            <div className="text-center">
              <div className="bg-orange-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-orange-600 font-bold">2</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Get Quote</h4>
              <p className="text-sm text-gray-600">
                We'll contact you within 24 hours with a detailed quote
              </p>
            </div>
            <div className="text-center">
              <div className="bg-orange-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-orange-600 font-bold">3</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Enjoy</h4>
              <p className="text-sm text-gray-600">
                We'll prepare and deliver your order on the specified date
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpecialOrders;
