import express from 'express';
import { body, query, validationResult } from 'express-validator';
import MenuItem from '../models/MenuItem.js';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Validation rules
const menuItemValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Description must be between 10 and 500 characters'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('category')
    .isIn(['main-course', 'side-dish', 'special', 'beverage', 'dessert'])
    .withMessage('Invalid category'),
  body('availability.daysOfWeek')
    .optional()
    .isArray()
    .withMessage('Days of week must be an array'),
  body('availability.daysOfWeek.*')
    .optional()
    .isIn(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
    .withMessage('Invalid day of week')
];

// @route   GET /api/menu
// @desc    Get all menu items (with filtering)
// @access  Public
router.get('/', optionalAuth, [
  query('category').optional().isIn(['main-course', 'side-dish', 'special', 'beverage', 'dessert']),
  query('day').optional().isIn(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']),
  query('available').optional().isBoolean(),
  query('special').optional().isBoolean(),
  query('search').optional().isString(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const {
      category,
      day,
      available = 'true',
      special,
      search,
      page = 1,
      limit = 20
    } = req.query;

    // Build query
    const query = { isActive: true };

    if (category) query.category = category;
    if (available === 'true') query['availability.isAvailable'] = true;
    if (special === 'true') query.isSpecial = true;
    if (day) {
      query.$or = [
        { 'availability.daysOfWeek': { $size: 0 } }, // Available all days
        { 'availability.daysOfWeek': day }
      ];
    }

    // Text search
    if (search) {
      query.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query
    const menuItems = await MenuItem.find(query)
      .populate('createdBy', 'name')
      .sort(search ? { score: { $meta: 'textScore' } } : { createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await MenuItem.countDocuments(query);

    // Filter by day availability if specified
    let filteredItems = menuItems;
    if (day) {
      filteredItems = menuItems.filter(item => item.isAvailableOnDay(day));
    }

    res.json({
      success: true,
      data: {
        menuItems: filteredItems,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get menu items error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get menu items',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/menu/:id
// @desc    Get single menu item
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const menuItem = await MenuItem.findOne({
      _id: req.params.id,
      isActive: true
    }).populate('createdBy', 'name');

    if (!menuItem) {
      return res.status(404).json({
        success: false,
        message: 'Menu item not found'
      });
    }

    res.json({
      success: true,
      data: { menuItem }
    });
  } catch (error) {
    console.error('Get menu item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get menu item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/menu
// @desc    Create new menu item
// @access  Private (Owner only)
router.post('/', authenticate, authorize('owner'), menuItemValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const menuItemData = {
      ...req.body,
      createdBy: req.user.id
    };

    const menuItem = new MenuItem(menuItemData);
    await menuItem.save();

    await menuItem.populate('createdBy', 'name');

    res.status(201).json({
      success: true,
      message: 'Menu item created successfully',
      data: { menuItem }
    });
  } catch (error) {
    console.error('Create menu item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create menu item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/menu/:id
// @desc    Update menu item
// @access  Private (Owner only)
router.put('/:id', authenticate, authorize('owner'), menuItemValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const menuItem = await MenuItem.findOneAndUpdate(
      { _id: req.params.id, isActive: true },
      req.body,
      { new: true, runValidators: true }
    ).populate('createdBy', 'name');

    if (!menuItem) {
      return res.status(404).json({
        success: false,
        message: 'Menu item not found'
      });
    }

    res.json({
      success: true,
      message: 'Menu item updated successfully',
      data: { menuItem }
    });
  } catch (error) {
    console.error('Update menu item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update menu item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   DELETE /api/menu/:id
// @desc    Delete menu item (soft delete)
// @access  Private (Owner only)
router.delete('/:id', authenticate, authorize('owner'), async (req, res) => {
  try {
    const menuItem = await MenuItem.findOneAndUpdate(
      { _id: req.params.id, isActive: true },
      { isActive: false },
      { new: true }
    );

    if (!menuItem) {
      return res.status(404).json({
        success: false,
        message: 'Menu item not found'
      });
    }

    res.json({
      success: true,
      message: 'Menu item deleted successfully'
    });
  } catch (error) {
    console.error('Delete menu item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete menu item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/menu/:id/availability
// @desc    Toggle menu item availability
// @access  Private (Owner only)
router.patch('/:id/availability', authenticate, authorize('owner'), async (req, res) => {
  try {
    const { isAvailable } = req.body;

    const menuItem = await MenuItem.findOneAndUpdate(
      { _id: req.params.id, isActive: true },
      { 'availability.isAvailable': isAvailable },
      { new: true }
    );

    if (!menuItem) {
      return res.status(404).json({
        success: false,
        message: 'Menu item not found'
      });
    }

    res.json({
      success: true,
      message: `Menu item ${isAvailable ? 'enabled' : 'disabled'} successfully`,
      data: { menuItem }
    });
  } catch (error) {
    console.error('Toggle availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update availability',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/menu/weekly/:day
// @desc    Get weekly menu for specific day
// @access  Public
router.get('/weekly/:day', optionalAuth, async (req, res) => {
  try {
    const { day } = req.params;
    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    
    if (!validDays.includes(day.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid day of week'
      });
    }

    const menuItems = await MenuItem.find({
      isActive: true,
      'availability.isAvailable': true,
      $or: [
        { 'availability.daysOfWeek': { $size: 0 } }, // Available all days
        { 'availability.daysOfWeek': day.toLowerCase() }
      ]
    }).populate('createdBy', 'name');

    // Filter items that are actually available on this day
    const availableItems = menuItems.filter(item => item.isAvailableOnDay(day));

    res.json({
      success: true,
      data: {
        day: day.toLowerCase(),
        menuItems: availableItems
      }
    });
  } catch (error) {
    console.error('Get weekly menu error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get weekly menu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
