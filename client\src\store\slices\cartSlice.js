import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  items: [],
  totalAmount: 0,
  totalItems: 0,
  isOpen: false,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action) => {
      const { menuItem, quantity = 1, specialInstructions = '' } = action.payload;
      
      // Check if item already exists in cart
      const existingItemIndex = state.items.findIndex(
        item => item.menuItem._id === menuItem._id && 
                item.specialInstructions === specialInstructions
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        state.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item to cart
        state.items.push({
          menuItem,
          quantity,
          specialInstructions,
          price: menuItem.price,
        });
      }

      // Recalculate totals
      cartSlice.caseReducers.calculateTotals(state);
    },
    
    removeFromCart: (state, action) => {
      const { menuItemId, specialInstructions = '' } = action.payload;
      state.items = state.items.filter(
        item => !(item.menuItem._id === menuItemId && 
                 item.specialInstructions === specialInstructions)
      );
      cartSlice.caseReducers.calculateTotals(state);
    },
    
    updateQuantity: (state, action) => {
      const { menuItemId, specialInstructions = '', quantity } = action.payload;
      
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        state.items = state.items.filter(
          item => !(item.menuItem._id === menuItemId && 
                   item.specialInstructions === specialInstructions)
        );
      } else {
        // Update quantity
        const itemIndex = state.items.findIndex(
          item => item.menuItem._id === menuItemId && 
                  item.specialInstructions === specialInstructions
        );
        
        if (itemIndex >= 0) {
          state.items[itemIndex].quantity = quantity;
        }
      }
      
      cartSlice.caseReducers.calculateTotals(state);
    },
    
    clearCart: (state) => {
      state.items = [];
      state.totalAmount = 0;
      state.totalItems = 0;
    },
    
    toggleCart: (state) => {
      state.isOpen = !state.isOpen;
    },
    
    openCart: (state) => {
      state.isOpen = true;
    },
    
    closeCart: (state) => {
      state.isOpen = false;
    },
    
    calculateTotals: (state) => {
      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.totalAmount = state.items.reduce(
        (total, item) => total + (item.price * item.quantity), 
        0
      );
    },
  },
});

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  toggleCart,
  openCart,
  closeCart,
  calculateTotals,
} = cartSlice.actions;

export default cartSlice.reducer;
